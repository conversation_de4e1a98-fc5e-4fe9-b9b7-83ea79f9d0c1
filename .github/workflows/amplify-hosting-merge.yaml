name: Deploy to Amplify on merge to main

on:
  workflow_dispatch:
    inputs:
      service:
        type: choice
        description: Service to deploy
        options:
          - safety
          - support
          - quoting
          - underwriter
          - graphiql
          - storybook
  push:
    branches:
      - main
permissions: write-all

concurrency:
  group: ${{ github.workflow }}-${{ github.sha }}
  cancel-in-progress: true

jobs:
  changes:
    runs-on: ubuntu-latest
    outputs:
      safety: ${{ steps.filter.outputs.safety }}
      ui-kit: ${{ steps.filter.outputs.ui-kit }}
      support: ${{ steps.filter.outputs.support }}
      storybook: ${{ steps.filter.outputs.storybook }}
      quoting: ${{ steps.filter.outputs.quoting }}
      underwriter: ${{ steps.filter.outputs.underwriter }}
      graphiql: ${{ steps.filter.outputs.graphiql }}
    steps:
      - uses: actions/checkout@v5
      - uses: dorny/paths-filter@v3
        id: filter
        with:
          filters: |
            safety:
              - 'src/nirvana/client/apps/safety/**'
              - "src/nirvana/client/package.json"
              - "src/nirvana/client/yarn.lock"
            ui-kit:
              - 'src/nirvana/client/packages/ui-kit/**'
              - "src/nirvana/client/packages/ui/**"
              - "src/nirvana/client/package.json"
              - "src/nirvana/client/yarn.lock"
            storybook:
              - "src/nirvana/client/apps/storybook/**"
              - "src/nirvana/client/package.json"
              - "src/nirvana/client/yarn.lock"
            support:
              - 'src/nirvana/client/apps/support/**'
              - "src/nirvana/client/package.json"
              - "src/nirvana/client/yarn.lock"
            quoting:
              - 'src/nirvana/client/apps/quoting/**'
              - "src/nirvana/client/package.json"
              - "src/nirvana/client/yarn.lock"
            underwriter:
              - 'src/nirvana/client/apps/underwriter/**'
              - "src/nirvana/client/package.json"
              - "src/nirvana/client/yarn.lock"
            graphiql:
              - "src/nirvana/client/apps/graphiql/**"
              - "src/nirvana/client/package.json"
              - "src/nirvana/client/yarn.lock"

  deploy-safety:
    needs: changes
    uses: ./.github/workflows/amplify-hosting.yaml
    with:
      app-id: dmmns8s1lobsz
      app-name: safety
      mode: production
      branch-name: main
      commit-hash: ${{ github.sha }}
      pull-request-number: 0
      # Works as a ternary operator if it is called from workflow_call and service is not safety skip, else run checks again output from changes
      skip: ${{ github.event_name == 'workflow_call' && inputs.service != 'safety' || (needs.changes.outputs.safety == 'false' && needs.changes.outputs.ui-kit == 'false' ) }}
    secrets:
      sentry-auth-token: ${{ secrets.SAFETY_SENTRY_AUTH_TOKEN }}
      webhook-url: ${{ secrets.FRONTEND_NOTIFICATION_SLACK_WEBHOOK_URL }}
      turborepo-token: ${{ secrets.TURBOREPO_TOKEN}}

  deploy-support:
    needs: changes
    uses: ./.github/workflows/amplify-hosting.yaml
    with:
      app-id: d2rhwh5sa9jmhi
      app-name: support
      mode: production
      branch-name: main
      commit-hash: ${{ github.sha }}
      pull-request-number: 0
      skip: ${{ github.event_name == 'workflow_call' && inputs.service != 'support' || (needs.changes.outputs.support == 'false' && needs.changes.outputs.ui-kit == 'false' ) }}
    secrets:
      sentry-auth-token: ${{ secrets.SUPPORT_SENTRY_AUTH_TOKEN }}
      webhook-url: ${{ secrets.FRONTEND_NOTIFICATION_SLACK_WEBHOOK_URL }}
      turborepo-token: ${{ secrets.TURBOREPO_TOKEN}}

  deploy-quoting:
    needs: changes
    uses: ./.github/workflows/amplify-hosting.yaml
    with:
      app-id: d1vlfpei1tktik
      app-name: quoting
      mode: production
      branch-name: main
      commit-hash: ${{ github.sha }}
      pull-request-number: 0
      skip: ${{  github.event_name == 'workflow_call' && inputs.service != 'quoting' || (needs.changes.outputs.quoting == 'false' && needs.changes.outputs.ui-kit == 'false' ) }}
    secrets:
      sentry-auth-token: ${{ secrets.QUOTING_SENTRY_AUTH_TOKEN }}
      webhook-url: ${{ secrets.FRONTEND_NOTIFICATION_SLACK_WEBHOOK_URL }}
      turborepo-token: ${{ secrets.TURBOREPO_TOKEN}}

  deploy-underwriter:
    needs: changes
    uses: ./.github/workflows/amplify-hosting.yaml
    with:
      app-id: d23z2wyvd07g2o
      app-name: underwriter
      mode: production
      branch-name: main
      commit-hash: ${{ github.sha }}
      pull-request-number: 0
      skip: ${{ github.event_name == 'workflow_call' && inputs.service != 'underwriter' || (needs.changes.outputs.underwriter == 'false' && needs.changes.outputs.ui-kit == 'false' ) }}
    secrets:
      sentry-auth-token: ${{ secrets.UNDERWRITER_SENTRY_AUTH_TOKEN }}
      webhook-url: ${{ secrets.FRONTEND_NOTIFICATION_SLACK_WEBHOOK_URL }}
      turborepo-token: ${{ secrets.TURBOREPO_TOKEN}}

  deploy-graphiql:
    needs: changes
    uses: ./.github/workflows/amplify-hosting.yaml
    with:
      app-id: d3sp3eglwcay59
      app-name: graphiql
      mode: production
      branch-name: main
      commit-hash: ${{ github.sha }}
      pull-request-number: 0
      skip: ${{ github.event_name == 'workflow_call' && inputs.service != 'graphiql' || (needs.changes.outputs.graphiql == 'false' && needs.changes.outputs.ui-kit == 'false' ) }}
    secrets:
      webhook-url: ${{ secrets.FRONTEND_NOTIFICATION_SLACK_WEBHOOK_URL }}
      turborepo-token: ${{ secrets.TURBOREPO_TOKEN}}

  deploy-storybook:
    needs: changes
    uses: ./.github/workflows/amplify-hosting.yaml
    with:
      app-id: d2gjvnfrie6ud4
      app-name: storybook
      mode: production
      branch-name: main
      commit-hash: ${{ github.sha }}
      pull-request-number: 0
      skip: ${{ github.event_name == 'workflow_call' && inputs.service != 'storybook' || (needs.changes.outputs.ui-kit == 'false' && needs.changes.outputs.storybook == 'false') }}
    secrets:
      webhook-url: ${{ secrets.FRONTEND_NOTIFICATION_SLACK_WEBHOOK_URL }}
      turborepo-token: ${{ secrets.TURBOREPO_TOKEN}}
