name: (Legacy) Push Images

permissions:
  contents: read
  id-token: write

on:
  workflow_dispatch:
    inputs:
      service:
        type: choice
        description: Service image to push
        options:
          - api_server
          - mvr_cache_server
          - pdfgen_server
          - oauth_server
          - graphql_server
          - job_processor
          - data_infra_job_processor
          - telematics_grpc_server
          - vehicles_grpc_server
          - feature_store_server
          - quoting_job_processor
          - distsem_server
          - truckercloud_auth
          - fmcsa_scraper
          - metaflow_metrics_lambda
          - nirvanamq
          - jobber_monitor
          - quote_scraper_grpc_server
          - grpc_lambda
          - saferwatch_scraper
          - event_job_processor
          - fmcsa_data_provider_grpc_server
          - mcp_experiments_rest_server
          - mcp_servers
          - llm_agents_claims
          - simulation_job_processor

defaults:
  run:
    shell: bash
    working-directory: src

jobs:
  push:
    uses: ./.github/workflows/reusable_push_images.yaml
    with:
      service: '["${{ inputs.service }}"]'
