---
- hosts: localhost
  connection: local
  gather_facts: true
  tasks:
    - name: install brew packages
      homebrew: name={{ item.name }}
      loop:
        - name: pyenv
        - name: go-task
        - name: openssl
        - name: readline
        - name: sqlite3
        - name: xz
        - name: zlib
        - name: gdal
        - name: cmake
        - name: poetry

    - name: Setup Pyenv and install dependencies with poetry
      shell: |
        pyenv install -v 3.9.11
        pyenv global 3.9.11
