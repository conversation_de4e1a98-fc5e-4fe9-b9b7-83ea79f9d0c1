# Create log group for service
resource "aws_cloudwatch_log_group" "simulation_job_processor" {
  name_prefix = "${terraform.workspace}-simulation-job-processor-logs"

  tags = {
    Environment = terraform.workspace
    Application = "simulation_job_processor"
  }
}

data "aws_iam_policy_document" "simulation_job_processor_tasks_policy_doc" {
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:ListBucket",
      "s3:GetObject",
      "s3:PutObject",
    ]
    resources = [
      data.aws_s3_bucket.quoting.arn,
      data.aws_s3_bucket.rateml_artifacts.arn,
      aws_s3_bucket.data_context_store.arn,
      aws_s3_bucket.pricing_simulations.arn,
    ]
  }

  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:DeleteObject",
    ]
    resources = [
      aws_s3_bucket.pricing_simulations.arn,
    ]
  }

  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "ecs:GetTaskProtection",
      "ecs:UpdateTaskProtection",
    ]
    resources = [
      "arn:aws:ecs:*:667656038718:task/*/*"
    ]
  }
}

resource "aws_iam_policy" "simulation_job_processor_tasks_policy" {
  policy = data.aws_iam_policy_document.simulation_job_processor_tasks_policy_doc.json
}

resource "aws_iam_role" "simulation_job_processor_task_role" {
  name               = "simulation_job_processor_task_role"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy.json
}

resource "aws_iam_role_policy_attachment" "simulationJobProcessorTaskRole_policy" {
  role       = aws_iam_role.simulation_job_processor_task_role.name
  policy_arn = aws_iam_policy.simulation_job_processor_tasks_policy.arn
}

# Create task definition
module "simulation_job_processor_td" {
  source = "../common-modules/ecs_task_definition"

  family         = "simulation-job-processor-td"
  container_name = "simulation-job-processor"

  container_image  = "${var.simulation_job_processor_ecr_repo_url}:${var.simulation_job_processor_tag}"
  task_memory     = 2048 # 2 GB
  task_cpu        = 1024 # 1 core
  essential        = true
  enable_otel_sidecar = true
  metrics_namespace   = "simulation_job_processor"
  sidecar_container_image = "${local.docker_hub_pull_through_cache_ecr_repo_url}/otel/opentelemetry-collector-contrib:0.117.0"

  port_mappings = [
    {
      containerPort = 56227
      hostPort      = 56227
      protocol      = "tcp"
    },
    # for pprof
    {
      containerPort = 6067
      hostPort      = 6067
      protocol      = "tcp"
    }
  ]

  map_environment = {
    "ENV"                                 = "prod"
    "DATABASES_NIRVANA_HOST"              = local.postgres_database_address
    "DATABASES_NIRVANA_NAME"              = "postgres"
    "DATABASES_NIRVANA_PORT"              = local.postgres_database_port
    "DATABASES_NIRVANA_USERNAME"          = local.postgres_database_readwrite_username
    "DATABASES_NIRVANA_PASSWORD"          = data.aws_secretsmanager_secret_version.app_db_readwrite_password.secret_string
    "SNOWFLAKE_FMCSA_PASSWORD"            = jsondecode(data.aws_secretsmanager_secret_version.snowflake_pass.secret_string)["prod_fmcsa_pass"]
    "DATABASES_NHTSA_HOST"                = local.nhtsa_database_address
    "DATABASES_NHTSA_PASSWORD"            = data.aws_secretsmanager_secret_version.nhtsa_db_password.secret_string

    "DATABASES_DS_HOST"                    = local.postgres_database_address
    "DATABASES_DS_NAME"                    = local.ds_database_name
    "DATABASES_DS_PORT"                    = local.postgres_database_port
    "DATABASES_DS_USERNAME"                = local.ds_database_username

    "DATABASES_FMCSA_HOST"                 = local.fmcsa_database_address_write
    "DATABASES_FMCSA_PORT"                 = local.fmcsa_database_port
    "DATABASES_FMCSA_NAME"                 = "postgres"
    "DATABASES_FMCSA_USERNAME"             = local.fmcsa_database_username

    "DATABASES_FMCSAWRITE_HOST"            = local.fmcsa_database_address_write
    "DATABASES_FMCSAWRITE_PORT"            = local.fmcsa_database_port
    "DATABASES_FMCSAWRITE_NAME"            = "postgres"
    "DATABASES_FMCSAWRITE_USERNAME"        = local.fmcsa_database_username

    "DATABASES_FMCSAREADONLY_HOST"         = local.fmcsa_database_address_read
    "DATABASES_FMCSAREADONLY_PORT"         = local.fmcsa_database_port
    "DATABASES_FMCSAREADONLY_NAME"         = "postgres"
    "DATABASES_FMCSAREADONLY_USERNAME"     = local.fmcsa_database_username

    "PRODUCTTOOLS_LAUNCHDARKLYAPIKEY"     = data.aws_secretsmanager_secret_version.launchdarkly_api_key.secret_string
    "PRODUCTTOOLS_SEGMENTAPIKEY"          = jsondecode(data.aws_secretsmanager_secret_version.segment_secrets.secret_string)["segment_api_key_prod"]
    "DBT_CLOUD_API_KEY"                   = jsondecode(data.aws_secretsmanager_secret_version.dbt_cloud_secrets.secret_string)["dbt_cloud_api_key"]
    "STATSD_RECEIVER_ADDRESS"             = "${local.otel_server_fqdn}:8125"
    "OTEL_EXPORTER_OTLP_ENDPOINT"         = "${local.otel_server_fqdn}:4317"
    "SAFETY_SENTURE_PASSWORD"             = jsondecode(data.aws_secretsmanager_secret_version.senture_sftp_pass.secret_string)["password"]
    "PAGERDUTY_SAFETY_ROUTINGKEY"         = jsondecode(data.aws_secretsmanager_secret_version.pagerduty_secrets.secret_string)["PAGERDUTY_SAFETY_ROUTINGKEY"]
    "SENDGRID_API_KEY"                    = jsondecode(data.aws_secretsmanager_secret_version.sendgrid_api_key.secret_string)["sendgrid_api_key"]
    "PRODUCTTOOLS_KNOCK_API_KEY"          = jsondecode(data.aws_secretsmanager_secret_version.knock_api_key.secret_string)["knock_api_key"]
    "PRODUCTTOOLS_POSTHOG_API_KEY"        = jsondecode(data.aws_secretsmanager_secret_version.posthog_personal_api_key.secret_string)["posthog_personal_api_key"]
    "PRODUCTTOOLS_POSTHOG_PUBLIC_API_KEY" = jsondecode(data.aws_secretsmanager_secret_version.posthog_public_api_key.secret_string)["posthog_public_api_key"]
    "INFRA_SLACK_EVENTS_TOKEN"            = jsondecode(data.aws_secretsmanager_secret_version.slack_events_jobber.secret_string)["oauth_access_token"]
  }

  map_secrets = {
    "DATABASES_DS_PASSWORD"                = data.aws_secretsmanager_secret_version.app_db_ds_user_password.arn
    "DATABASES_FMCSA_PASSWORD"             = aws_secretsmanager_secret_version.db_fmcsa_password_val.arn
    "DATABASES_FMCSAREADONLY_PASSWORD"     = aws_secretsmanager_secret_version.db_fmcsa_password_val.arn
    "DATABASES_FMCSAWRITE_PASSWORD"        = aws_secretsmanager_secret_version.db_fmcsa_password_val.arn
    "DATABASES_SNOWFLAKE_USERS_SNAPSHEET_PRIVATEKEY"  = "${data.aws_secretsmanager_secret_version.snapsheet_snowflake_credentials.arn}:privateKey::"
    "PRODUCTTOOLS_CLERKCONFIG_SECRETKEY"   = data.aws_secretsmanager_secret_version.clerk_secret.arn
  }

  log_configuration = {
    "logDriver" = "awslogs"
    "options" = {
      "awslogs-group"         = aws_cloudwatch_log_group.simulation_job_processor.name
      "awslogs-region"        = "us-east-2"
      "awslogs-stream-prefix" = "awslogs-simulation-job-processor"
    }
  }

  task_role_arn           = aws_iam_role.simulation_job_processor_task_role.arn
  task_execution_role_arn = aws_iam_role.ecsTaskExecutionRole.arn

  tags = {
    Environment = terraform.workspace
    Application = "simulation-job-processor"
  }
}

# Create ECS Fargate service
module "simulation_job_processor_fg_service" {
  source = "../common-modules/nv_fg_svc"

  name_prefix    = "simulation-job-processor"
  container_name = "simulation-job-processor"
  container_port = 56226

  vpc_id           = local.default_vpc_id
  ecs_cluster_arn  = local.internal_tools_cluster_arn
  ecs_cluster_name = local.internal_tools_cluster_name

  task_definition_arn = module.simulation_job_processor_td.arn

  desired_count = 1
  deployment_maximum_percent         = 200
  deployment_minimum_healthy_percent = 100

  private_subnets = [
    local.private_subnet_ids[0],
    local.private_subnet_ids[1],
    local.private_subnet_ids[2],
  ]
  security_groups = [local.default_sg_id]

  service_discovery_namespace_id = local.app_cluster_dns_id
  tags = {
    Environment = terraform.workspace
    Application = "simulation-job-processor"
  }

  # Adding cloudwatch alerts for CPU/Memory utilization
  cloudwatch_alarms_config = {
    cpu_alarm = {
      threshold    = 80,
      team_sns_arn = local.cloudwatch_to_pagerduty_safety_sns_arn,
    },
    memory_alarm = {
      threshold    = 80,
      team_sns_arn = local.cloudwatch_to_pagerduty_safety_sns_arn,
    }
  }
}

locals {
  simulation_job_processor_fqdn = "${module.simulation_job_processor_fg_service.discovery_service_name}.${local.app_cluster_dns_name}"
}