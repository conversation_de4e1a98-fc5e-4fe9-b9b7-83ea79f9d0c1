# Use an official Python runtime as a parent image
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV UV_HOME=/opt/uv
ENV PATH="/opt/uv/bin:$PATH"

# Install uv and system dependencies
RUN set -e && \
    mkdir -p /opt/uv/bin && \
    apt-get update && apt-get install -y curl build-essential libpq-dev && apt-get clean && \
    curl -LsSf https://astral.sh/uv/install.sh | UV_INSTALL_DIR=/opt/uv/bin sh && \
    echo "Contents of /opt/uv/bin:" && ls -la /opt/uv/bin && \
    /opt/uv/bin/uv --version # Verify uv installation

# Set the working directory in the container
WORKDIR /app

# Copy the entire workspace structure for proper dependency resolution
COPY pyproject.toml ./pyproject.toml
COPY uv.lock ./uv.lock

# Copy all workspace members
COPY claims_agent/ ./claims_agent/
COPY nirvana_rest_api/ ./nirvana_rest_api/
COPY nirvana_commons/ ./nirvana_commons/

# Install the entire workspace with all dependencies
RUN /opt/uv/bin/uv sync --all-packages

# Expose the port the app runs on
EXPOSE 8000

# Define the command to run the app
# Use uvicorn directly from the virtual environment
CMD ["/app/.venv/bin/uvicorn", "claims_agent.main:app", "--host", "0.0.0.0", "--port", "8000"] 