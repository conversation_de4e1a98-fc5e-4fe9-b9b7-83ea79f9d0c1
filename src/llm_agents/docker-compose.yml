# This Docker Compose file is organized into profiles for managing different parts of the application stack.
#
# Available profiles:
#   - temporal:     Runs only the Temporal services (temporal_db, temporal, temporal_admin, temporal_ui).
#   - redis:        Runs the Redis service.
#   - infra:        Runs all backing services (Temporal stack and Redis).
#   - apps:         Runs the main application services (claims_agent, mcp_servers).
#
# Usage:
#   - To start only Temporal:
#     docker compose --profile temporal up
#
#   - To start only Redis:
#     docker compose --profile redis up
#
#   - To start all infrastructure:
#     docker compose --profile infra up
#
#   - To start multiple profiles (eg: infra and apps):
#     docker compose --profile infra --profile apps up
#
#   - To run all profiles:
#     docker compose --profile "*" up

include:
  - docker-compose.supabase.yml

services:

  temporal:
    image: ${PULL_THROUGH_CACHE_BASE}temporalio/auto-setup:1.21.0
    container_name: temporal
    profiles:
      - "temporal"
      - "infra"
    ports:
      - "7233:7233" # gRPC
      - "8233:8233" # Web UI
    environment:
      - DB=postgresql
      - DB_PORT=5432
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_PWD=${POSTGRES_PASSWORD}
      - POSTGRES_SEEDS=db
      - POSTGRES_INITIAL_DB=postgres
      - POSTGRES_DB=temporal
      - POSTGRES_VISIBILITY_DB=temporal_visibility
    healthcheck:
      test:
        [
          "CMD",
          "tctl",
          "--address",
          "temporal:7233",
          "workflow",
          "list"
        ]
      interval: 1s
      timeout: 5s
      retries: 30
    depends_on:
      db:
        condition: service_healthy
      supavisor:
        condition: service_healthy

  temporal_admin:
    image: ${PULL_THROUGH_CACHE_BASE}temporalio/admin-tools:1.21.0
    container_name: temporal_admin
    profiles:
      - "temporal"
      - "infra"
    stdin_open: true
    tty: true
    depends_on:
      - temporal
    environment:
      - TEMPORAL_CLI_ADDRESS=temporal:7233

  temporal_ui:
    image: ${PULL_THROUGH_CACHE_BASE}temporalio/ui:2.21.3
    container_name: temporal_ui
    profiles:
      - "temporal"
      - "infra"
    ports:
      - "8080:8080"
    environment:
      - TEMPORAL_ADDRESS=temporal:7233
      - TEMPORAL_CORS_ORIGINS=http://localhost:8080
    depends_on:
      temporal:
        condition: service_healthy

  redis:
    image: ${PULL_THROUGH_CACHE_BASE}redis:alpine
    container_name: mcp_redis
    profiles:
      - "redis"
      - "infra"
    command: redis-server --requirepass lebowski
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  claims_agent:
    build:
      context: .
      dockerfile: ./claims_agent/Dockerfile
    container_name: claims_agent_dev
    profiles:
      - "apps"
    ports:
      - "8000:8000"
    volumes:
      # Mount the whole claims_agent directory for src changes and pyproject.toml changes
      - ./claims_agent:/app/claims_agent
      # We also need to mount the root pyproject and lockfile if they change for workspace deps
      - ./pyproject.toml:/app/pyproject.toml
      - ./uv.lock:/app/uv.lock
    env_file:
     - ./claims_agent/.env 
    environment:
      - TEMPORAL_ADDRESS=temporal:7233
      - SUPABASE_DB_URL=postgresql+psycopg://postgres:${POSTGRES_PASSWORD}@db:5432/postgres?sslmode=disable
      - CLAIMS_SERVER_URL=http://mcp_servers:8001/claims/sse
      - POLICY_SERVER_URL=http://mcp_servers:8001/policy/sse
    depends_on:
      migrator:
        condition: service_completed_successfully
      temporal:
        condition: service_healthy
    command: /app/.venv/bin/uvicorn claims_agent.main:app --reload --host 0.0.0.0 --port 8000

  claims_agent_worker:
    build:
      context: .
      dockerfile: ./claims_agent/Dockerfile
    container_name: claims_agent_worker_dev
    profiles:
      - "apps"
    volumes:
      # Mount the whole claims_agent directory for src changes and pyproject.toml changes
      - ./claims_agent:/app/claims_agent
      # We also need to mount the root pyproject and lockfile if they change for workspace deps
      - ./pyproject.toml:/app/pyproject.toml
      - ./uv.lock:/app/uv.lock
    env_file:
     - ./claims_agent/.env
    environment:
      - TEMPORAL_ADDRESS=temporal:7233
      - SUPABASE_DB_URL=postgresql+psycopg://postgres:${POSTGRES_PASSWORD}@db:5432/postgres?sslmode=disable
      - CLAIMS_SERVER_URL=http://mcp_servers:8001/claims/sse
      - POLICY_SERVER_URL=http://mcp_servers:8001/policy/sse
    depends_on:
      migrator:
        condition: service_completed_successfully
      temporal:
        condition: service_healthy
    command: /app/.venv/bin/python -m claims_agent.background.worker
    
  mcp_servers:
    build:
      context: .
      dockerfile: ./mcp_servers/Dockerfile
    container_name: mcp_servers_dev
    profiles:
      - "apps"
    ports:
      - "8001:8001"
    volumes:
      - ./mcp_servers:/app/mcp_servers
      - ./pyproject.toml:/app/pyproject.toml
      - ./uv.lock:/app/uv.lock
    env_file:
     - ./mcp_servers/.env 
    environment:
      # For mcp_servers, provide individual Redis components
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - REDIS_PASSWORD=lebowski # This comes from the redis service command
      - REDIS_SSL=false
      # LLAMA_CLOUD_API_KEY will be loaded from ./mcp_servers/.env via env_file
      # SENTRY_DSN will be loaded from ./mcp_servers/.env via env_file
      # ENVIRONMENT will be loaded from ./mcp_servers/.env via env_file
      # LLAMA_PARSE_STORAGE_NAMESPACE will be loaded from ./mcp_servers/.env via env_file
    depends_on:
      - redis
    command: /app/.venv/bin/uvicorn mcp_servers.main:app --reload --host 0.0.0.0 --port 8001

  migrator:
    build:
      context: ./db
      dockerfile: Dockerfile
    container_name: migrator
    profiles:
      - "apps"
    environment:
      - DATABASE_URL=postgres://postgres:${POSTGRES_PASSWORD}@db:5432/postgres?sslmode=disable
      - DBMATE_MIGRATIONS_DIR=./migrations
    volumes:
      - ./db:/db
    depends_on:
      db:
        condition: service_healthy
      supavisor:
        condition: service_healthy
    command: ["up"]

volumes:
  redis_data: