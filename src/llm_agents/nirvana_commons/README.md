# Nirvana Commons

Shared utilities and common components for Nirvana services.

## Overview

`nirvana-commons` provides reusable components that are shared across multiple services in the Nirvana ecosystem. This package helps maintain consistency and reduce code duplication.

## Components

### Authentication (`nirvana_commons.auth`)

Provides unified authentication components for Nirvana services:

- **Token Verification**: Validates tokens against Nirvana REST API with 5-minute caching
- **Authentication Backends**: Starlette-compatible auth backends for single and multi-header authentication
- **User Context Middleware**: Extracts user information from authenticated requests
  - Stores user email in both `request.state` and ContextVar for maximum compatibility
  - Supports both authenticated user objects and JWT token extraction
  - Provides `get_user_email()` and `set_user_email()` functions for ContextVar access
  - Integrates with OpenTelemetry for distributed tracing

#### Usage Example

```python
from starlette.middleware.authentication import AuthenticationMiddleware
from nirvana_commons.auth import (
    NirvanaAuthBackend, 
    NirvanaTokenVerifier,
    UserContextMiddleware,
    get_user_email
)
from nirvana_commons.auth.backends import on_auth_error

# For single-header authentication (e.g., MCP servers)
app.add_middleware(
    AuthenticationMiddleware,
    backend=NirvanaAuthBackend(
        paths_to_skip=["/health"],
        token_verifier=NirvanaTokenVerifier(),
    ),
    on_error=on_auth_error,
)

# For multi-header authentication (e.g., Claims Agent)
from nirvana_commons.auth import MultiHeaderAuthBackend

app.add_middleware(
    AuthenticationMiddleware,
    backend=MultiHeaderAuthBackend(
        accepted_headers=["Clerk-Authorization", "Authorization", "JSESSIONID"],
        paths_to_skip=["/health"],
        token_verifier=NirvanaTokenVerifier(),
    ),
    on_error=on_auth_error,
)

# Add user context extraction (after authentication middleware)
app.add_middleware(UserContextMiddleware)

# Access user email in your handlers
@app.get("/api/resource")
async def get_resource():
    user_email = get_user_email()  # From ContextVar
    # ... use user_email for audit logging, etc.
```

## Installation

In your service's `pyproject.toml`:

```toml
[project]
dependencies = [
    "nirvana-commons",
    # ... other dependencies
]

[tool.uv.sources]
nirvana-commons = {path = "../nirvana_commons", editable = true}
```

## Development

### Running Tests

```bash
# Run auth tests
uv run pytest nirvana_commons/tests/auth/

# Run all commons tests
uv run pytest nirvana_commons/tests/
```

### Adding New Components

When adding new shared utilities:

1. Create a new module under `src/nirvana_commons/`
2. Add corresponding tests under `tests/`
3. Update this README with usage examples
4. Export public APIs in the module's `__init__.py`

## Future Components

Planned additions to nirvana-commons:

- **Logging**: Shared logging configuration and middleware
- **Redis**: Common Redis client management
- **API Utilities**: Shared Nirvana API client helpers
- **Enums**: Common enumerations used across services
- **Configuration**: Shared configuration utilities

## Future Authentication Improvements

The following improvements are planned for the authentication module:

### 1. Configuration Consolidation (Priority: MEDIUM)
**Effort**: 2 days | **Risk**: Low | **Service Impact**: Both services (3-4 files total)

- Create unified `AuthConfig` dataclass encapsulating:
  - `nirvana_api_base_url`
  - `paths_to_skip`
  - `accepted_headers` (for multi-header support)
  - `support_dev_fallback` and `dev_fallback_token`
  - Token cache settings (TTL, max size)
- Benefits: Centralized configuration, easier testing, clearer settings management
- Can be introduced alongside existing configuration for gradual migration

### 2. Consolidate Authentication Backends (Priority: MEDIUM)
**Effort**: 2-3 days | **Risk**: Medium | **Service Impact**: Both services (2 files + tests)

- Merge `NirvanaAuthBackend` and `MultiHeaderAuthBackend` into single flexible backend
- Design approach:
  ```python
  NirvanaAuthBackend(
      token_verifier=...,
      paths_to_skip=[...],
      accepted_headers=["Authorization"],  # Single header by default
      # Multi-header params optional
      support_dev_fallback=False,
      dev_fallback_token=None
  )
  ```
- Maintain backward compatibility with adapter classes if needed
- Current usage:
  - mcp_servers: Uses `NirvanaAuthBackend` with single "Authorization" header
  - claims_agent: Uses `MultiHeaderAuthBackend` with 3 headers

### 3. JWT Library Integration (Priority: LOW)
**Effort**: 1-2 days | **Risk**: Medium-High | **Service Impact**: None (internal to nirvana_commons)

- Replace manual base64 JWT decoding with `python-jose` or `PyJWT`
- Benefits: Safer parsing, built-in validation, standard compliance
- Risks: New dependency, potential edge case differences
- Note: Keep manual parsing as fallback during transition
- Currently extracts: `primary_email_address` (Clerk) and `email` (standard)

## Implementation Notes

### Current Authentication Flow
1. Request arrives with auth header(s)
2. `AuthenticationMiddleware` (Starlette) invokes backend
3. Backend extracts token and calls `NirvanaTokenVerifier`
4. Verifier validates against Nirvana REST API `/me` endpoint (with 5-minute cache)
5. Returns `AccessToken` with scopes based on user roles
6. `UserContextMiddleware` extracts email for logging/tracing

These improvements will be implemented incrementally to ensure backward compatibility and minimize risk.