[project]
name = "nirvana-commons"
description = "Shared utilities and common components for Nirvana services"
version = "0.1.0"
authors = [
    {name = "Nirvana Team", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "mcp>=1.2.0",
    "nirvana-rest-api",
    "starlette>=0.37.0",
    "httpx>=0.25.0",
    "async-lru>=2.0.4",
    "pydantic>=2.0.0",
    "loguru>=0.7.0",
]

[tool.uv.sources]
nirvana-rest-api = {workspace = true}

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/nirvana_commons"]

# Ruff ignores
[tool.ruff.lint.per-file-ignores]
"tests/**/*.py" = ["S101"]
