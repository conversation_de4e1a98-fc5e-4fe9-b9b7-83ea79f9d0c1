"""Authentication backends for Nirvana services.

This module provides Starlette authentication backends that integrate with
the Nirvana REST API for token validation.
"""

import time
from http import HTT<PERSON>tatus

from loguru import logger
from mcp.server.auth.middleware.bearer_auth import Authenticated<PERSON>ser, BearerAuthBackend
from mcp.server.auth.provider import TokenVerifier
from starlette.authentication import (
    AuthCredentials,
    AuthenticationError,
    BaseUser,
    SimpleUser,
)
from starlette.requests import HTTPConnection
from starlette.responses import JSONResponse, Response


class NirvanaAuthBackend(BearerAuthBackend):
    """Starlette authentication backend for Nirvana services.

    This backend:
    - Validates Bearer tokens using the provided TokenVerifier
    - Supports path-based authentication skipping
    - Returns appropriate auth credentials and user information
    - Provides user-friendly error messages

    This class extends the MCP SDK's BearerAuthBackend to provide
    better error messages and path skipping functionality.
    """

    def __init__(self, paths_to_skip: list[str], token_verifier: TokenVerifier) -> None:
        """Initialize the authentication backend.

        Args:
            paths_to_skip: URL paths that don't require authentication
                         (e.g., '/health', '/docs').
            token_verifier: The token verifier to use for authentication.
        """
        self._paths_to_skip = paths_to_skip
        super().__init__(token_verifier)

    async def authenticate(
        self, conn: HTTPConnection
    ) -> tuple[AuthCredentials, BaseUser] | None:
        """Authenticate the connection based on the Authorization header.

        Args:
            conn: The incoming Starlette HTTPConnection.

        Returns:
            A tuple of (AuthCredentials, BaseUser) if authentication succeeds.
            None if the path is configured to be skipped.

        Raises:
            AuthenticationError: If authentication fails.
        """
        path = conn.url.path
        if path in self._paths_to_skip:
            logger.trace(f"Skipping auth for path: {path}")
            return None

        logger.trace(f"Attempting authentication for path: {path}")
        auth_header = conn.headers.get("Authorization")
        if not auth_header:
            logger.warning(
                f"Authentication failed: No Authorization header. Path: {path}"
            )
            raise AuthenticationError("Authorization header is required.")

        # Validate header format
        parts = auth_header.split()
        if len(parts) != 2 or parts[0].lower() != "bearer":
            logger.warning(
                f"Authentication failed: Invalid Authorization header format. Path: {path}"
            )
            raise AuthenticationError(
                "Invalid Authorization header format. Expected 'Bearer <token>'."
            )

        token = parts[1]

        # Validate the token with the verifier
        auth_info = await self.token_verifier.verify_token(token)

        if not auth_info:
            return None

        if auth_info.expires_at and auth_info.expires_at < int(time.time()):
            return None

        return AuthCredentials(auth_info.scopes), AuthenticatedUser(auth_info)


class MultiHeaderAuthBackend(NirvanaAuthBackend):
    """Authentication backend supporting multiple authentication headers.

    This backend extends NirvanaAuthBackend to support multiple header names
    for authentication, checking them in priority order. This is useful for
    services that need to support multiple authentication schemes.
    """

    def __init__(
        self,
        accepted_headers: list[str],
        paths_to_skip: list[str],
        token_verifier: TokenVerifier,
        *,
        support_dev_fallback: bool = False,
        dev_fallback_token: str | None = None,
    ) -> None:
        """Initialize the multi-header authentication backend.

        Args:
            accepted_headers: List of header names to check for authentication,
                            in priority order (e.g., ["Clerk-Authorization",
                            "Authorization", "JSESSIONID"]).
            paths_to_skip: URL paths that don't require authentication.
            token_verifier: The token verifier to use for authentication.
            support_dev_fallback: Whether to support development fallback token.
            dev_fallback_token: The fallback token to use in development mode.
        """
        self.accepted_headers = accepted_headers
        self.support_dev_fallback = support_dev_fallback
        self.dev_fallback_token = dev_fallback_token
        super().__init__(paths_to_skip, token_verifier)

    async def authenticate(
        self, conn: HTTPConnection
    ) -> tuple[AuthCredentials, BaseUser] | None:
        """Authenticate using multiple possible headers.

        Checks each header in the accepted_headers list in order,
        using the first valid token found.

        Args:
            conn: The incoming Starlette HTTPConnection.

        Returns:
            A tuple of (AuthCredentials, BaseUser) if authentication succeeds.
            None if the path is configured to be skipped.

        Raises:
            AuthenticationError: If no valid authentication is found.
        """
        path = conn.url.path
        if path in self._paths_to_skip:
            logger.trace(f"Skipping auth for path: {path}")
            return None

        # Skip authentication for OPTIONS requests (CORS preflight)
        if hasattr(conn, "scope") and conn.scope.get("method") == "OPTIONS":
            logger.trace(f"Skipping auth for OPTIONS request: {path}")
            return None

        logger.trace(f"Attempting multi-header authentication for path: {path}")

        # Try each accepted header in priority order
        token = None
        used_header = None

        for header_name in self.accepted_headers:
            header_value = conn.headers.get(header_name)
            if not header_value:
                continue

            # Extract token based on header type
            if header_name == "JSESSIONID":
                # JSESSIONID doesn't use Bearer prefix
                extracted_token = header_value.strip()
            else:
                # Other headers require Bearer prefix
                if header_value.lower().startswith("bearer "):
                    extracted_token = header_value[7:].strip()
                else:
                    continue

            # Use first non-empty token found
            if extracted_token:
                token = extracted_token
                used_header = header_name
                break

        # Development fallback
        if not token and self.support_dev_fallback and self.dev_fallback_token:
            logger.debug("Using development fallback token")
            token = self.dev_fallback_token
            used_header = "DEV_FALLBACK"

        if not token:
            logger.warning(
                f"Authentication failed: No valid token found in headers {self.accepted_headers}. Path: {path}"
            )
            raise AuthenticationError(
                f"Authorization header with Bearer token is required. "
                f"Accepted headers: {', '.join(self.accepted_headers)}"
            )

        logger.debug(f"Using {used_header} header for authentication")

        # Validate the token
        try:
            auth_info = await self.token_verifier.verify_token(token)
        except AuthenticationError:
            # Re-raise authentication errors as-is
            raise
        except Exception as e:
            logger.error(f"Token verification error: {e}")
            raise AuthenticationError("Token verification failed") from e

        if not auth_info:
            raise AuthenticationError("Invalid or expired token")

        if auth_info.expires_at and auth_info.expires_at < int(time.time()):
            raise AuthenticationError("Token has expired")

        # Store the token in request state for downstream use
        conn.state.auth_token = token
        conn.state.auth_header_used = used_header

        # For backward compatibility, create a simple user if scopes aren't needed
        if not auth_info.scopes or auth_info.scopes == ["authenticated"]:
            return AuthCredentials(["authenticated"]), SimpleUser(auth_info.client_id)

        return AuthCredentials(auth_info.scopes), AuthenticatedUser(auth_info)


def on_auth_error(conn: HTTPConnection, exc: AuthenticationError) -> Response:
    """Handle authentication errors for Starlette AuthenticationMiddleware.

    Args:
        conn: The HTTP connection associated with the request.
        exc: The AuthenticationError that was raised.

    Returns:
        A JSONResponse with a 401 status code and error detail.
    """
    logger.info(f"Authentication error on path {conn.url.path}: {exc}")
    return JSONResponse(
        {"detail": str(exc)},
        status_code=HTTPStatus.UNAUTHORIZED,
        headers={"WWW-Authenticate": "Bearer"},
    )
