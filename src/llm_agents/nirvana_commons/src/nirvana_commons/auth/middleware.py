"""Optional middleware helpers for Nirvana authentication."""

import base64
import json
from collections.abc import Awaitable, Callable
from contextvars import ContextV<PERSON>
from typing import Any

from loguru import logger
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from starlette.responses import Response


# ContextVar for storing user email across async context
_user_email_var: ContextVar[str | None] = ContextVar("user_email", default=None)


def set_user_email(email: str | None) -> None:
    """Store email for the current async context.

    This is used for compatibility with claims_agent which expects
    user email to be available via ContextVar.

    Args:
        email: The user's email address or None.
    """
    _user_email_var.set(email)


def get_user_email() -> str | None:
    """Return the user email for the current async context.

    Returns:
        The user's email address if set, None otherwise.
    """
    return _user_email_var.get()


def extract_email_from_jwt(token: str) -> str | None:
    """Extract the email claim from a JWT token without verification.

    This function only decodes the JWT payload to extract the email claim.
    It does NOT verify the token signature - this is intended for logging
    and tracing purposes only.

    Args:
        token: The JWT token string (without Bearer prefix).

    Returns:
        The email address if found in the token, None otherwise.
    """
    try:
        parts = token.split(".")
        if len(parts) < 2:
            return None

        payload_b64 = parts[1]
        # JWT uses URL-safe base64 without padding
        padding = "=" * (-len(payload_b64) % 4)
        payload_json = base64.urlsafe_b64decode(payload_b64 + padding).decode()
        payload: dict[str, Any] = json.loads(payload_json)

        # Check for email in common JWT claims
        # Clerk uses primary_email_address
        return payload.get("primary_email_address") or payload.get("email")
    except Exception as e:
        logger.debug(f"Unable to extract email from JWT: {e}")
        return None


class UserContextMiddleware(BaseHTTPMiddleware):
    """Middleware to extract and store user context from authenticated requests.

    This middleware should be placed AFTER authentication middleware. It extracts
    user information from the authenticated request and makes it available for
    logging, tracing, and request handling.

    The middleware:
    - Extracts user email from the authenticated user object
    - Falls back to JWT decoding if needed
    - Stores the email in request.state for easy access
    - Optionally adds OpenTelemetry trace attributes
    """

    def __init__(self, app: Any, *, extract_from_jwt: bool = True) -> None:
        """Initialize the middleware.

        Args:
            app: The ASGI application.
            extract_from_jwt: Whether to fall back to JWT extraction if
                            user email is not available from auth.
        """
        super().__init__(app)
        self.extract_from_jwt = extract_from_jwt

    async def dispatch(
        self, request: Request, call_next: Callable[[Request], Awaitable[Response]]
    ) -> Response:
        """Process the request to extract user context."""
        user_email = None

        # First try to get email from authenticated user
        # Note: request.user will throw AssertionError if AuthenticationMiddleware
        # hasn't run or if the request is unauthenticated (e.g., OPTIONS)
        try:
            if hasattr(request.user, "username"):
                # In our case, username is the email
                user_email = request.user.username
            elif hasattr(request.user, "access_token"):
                # For MCP AuthenticatedUser
                user_email = request.user.access_token.client_id
        except (AttributeError, AssertionError):
            # No authenticated user available
            pass

        # Fall back to JWT extraction if enabled and no email found
        if not user_email and self.extract_from_jwt:
            for header_name in ["Clerk-Authorization", "Authorization"]:
                header_value = request.headers.get(header_name)
                if header_value and header_value.lower().startswith("bearer "):
                    token = header_value[7:].strip()
                    user_email = extract_email_from_jwt(token)
                    if user_email:
                        break

        # Store in request state for easy access
        request.state.user_email = user_email

        # Also store in ContextVar for claims_agent compatibility
        set_user_email(user_email)

        # Add OpenTelemetry trace attribute if available
        try:
            from opentelemetry import trace

            if user_email:
                span = trace.get_current_span()
                if span:
                    span.set_attribute("user.email", user_email)
        except ImportError:
            # OpenTelemetry not available, skip
            pass

        # Continue processing
        response = await call_next(request)

        # Clear the context var after request processing
        set_user_email(None)

        return response
