"""Token verifier implementation for Nirvana authentication."""

from async_lru import alru_cache
from loguru import logger
from mcp import <PERSON>rrorData, McpError
from mcp.server.auth.provider import AccessToken, TokenVerifier
from mcp.types import INTERNAL_ERROR, PARSE_ERROR
from starlette.authentication import AuthenticationError

from nirvana_rest_api.api.auth.get_me import asyncio_detailed as get_me
from nirvana_rest_api.client import AuthenticatedClient
from nirvana_rest_api.models.roles import Roles
from nirvana_rest_api.types import UNSET, Unset


class NirvanaTokenVerifier(TokenVerifier):
    """Token verifier that validates tokens against Nirvana REST API.

    This verifier:
    - Validates tokens by calling the Nirvana REST API /me endpoint
    - Caches validation results for improved performance
    - Extracts user roles and converts them to scopes
    - Handles various error scenarios gracefully
    """

    def __init__(
        self, nirvana_api_base_url: str = "https://api.prod.nirvanatech.com"
    ) -> None:
        """Initialize the token verifier.

        Args:
            nirvana_api_base_url: Base URL for the Nirvana REST API.
                                Defaults to production API.
        """
        self.nirvana_api_base_url = nirvana_api_base_url

    # Bound the cache with maxsize and ttl (5 minutes to match Clerk token expiry)
    @alru_cache(maxsize=128, ttl=300)
    async def verify_token(self, token: str) -> AccessToken | None:
        """Verify a token and return access token information.

        Args:
            token: The bearer token to verify.

        Returns:
            AccessToken with user information and scopes if valid, None otherwise.

        Raises:
            AuthenticationError: If the token is invalid or expired.
            McpError: If there's an error communicating with the Nirvana API.
        """
        if not token or not token.strip():
            raise AuthenticationError("No token provided")

        logger.debug(f"Verifying token: {token[:10]}...")

        async with AuthenticatedClient(
            base_url=self.nirvana_api_base_url,
            token=token,
            # Nirvana API expects Clerk-Authorization header only
            auth_header_name="Clerk-Authorization",
            prefix="Bearer",
        ) as client:
            response = await get_me(client=client)

        # Handle different response statuses
        if response.status_code >= 500:
            raise McpError(
                ErrorData(
                    code=INTERNAL_ERROR,
                    message=f"Received a {response.status_code} error from the Nirvana REST API",
                )
            )
        if response.status_code >= 400:
            logger.warning(f"Nirvana API returned 4xx error: {response.status_code}.")
            # 401/403 often mean invalid/expired token
            error_message = (
                f"Authentication error ({response.status_code}) from Nirvana REST API"
            )
            if response.status_code in (401, 403):
                error_message = f"Invalid or expired token ({response.status_code})"
            raise AuthenticationError(error_message)
        if response.status_code != 200:
            logger.error(f"Nirvana API returned non-200 status: {response.status_code}")
            raise McpError(
                ErrorData(
                    code=PARSE_ERROR,
                    message=f"Received non-200 response ({response.status_code}) from Nirvana REST API",
                )
            )
        if response.parsed is None:
            logger.error("Nirvana API returned 200 but failed to parse response")
            raise McpError(
                ErrorData(
                    code=PARSE_ERROR,
                    message=f"Failed to parse valid response from Nirvana REST API (status {response.status_code})",
                )
            )

        user_info = response.parsed
        scopes = self._extract_scopes_from_roles(user_info.roles)

        return AccessToken(
            token=token,
            client_id=user_info.email,  # Using email as client_id
            scopes=scopes,
            expires_at=None,  # Token expiration is handled by Nirvana API
        )

    def _extract_scopes_from_roles(self, roles: Unset | Roles) -> list[str]:
        """Extract scope strings from user roles.

        Maps Nirvana role types to scopes:
        - nirvana_roles -> nirvana:*
        - agency_roles -> agency:*
        - fleet_roles -> fleet:*

        Args:
            roles: The roles object from the user info.

        Returns:
            List of scope strings extracted from roles.
        """
        scopes: list[str] = ["authenticated"]  # Always include authenticated scope

        if roles is UNSET:
            return scopes

        role_mapping = {
            "nirvana_roles": "nirvana",
            "agency_roles": "agency",
            "fleet_roles": "fleet",
        }

        for role_type_attr, scope_prefix in role_mapping.items():
            role_list = getattr(roles, role_type_attr, None)
            if role_list:
                for role in role_list:
                    if role is not UNSET and hasattr(role, "role"):
                        scopes.append(f"{scope_prefix}:{role.role}")

        return scopes
