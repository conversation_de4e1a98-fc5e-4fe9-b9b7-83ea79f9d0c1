"""Test configuration and fixtures for nirvana_auth tests."""

import pytest
from mcp.server.auth.provider import AccessToken


@pytest.fixture
def valid_token() -> str:
    """Return a valid test token."""
    return "test-bearer-token-123"


@pytest.fixture
def valid_access_token():
    """Return a valid AccessToken object."""
    return AccessToken(
        token="test-bearer-token-123",
        client_id="<EMAIL>",
        scopes=["authenticated", "nirvana:admin", "agency:viewer"],
        expires_at=None,
    )


@pytest.fixture
def mock_headers():
    """Return mock headers for testing."""
    return {
        "Authorization": "Bearer test-bearer-token-123",
        "Content-Type": "application/json",
    }


@pytest.fixture
def mock_headers_clerk():
    """Return mock headers with Clerk-Authorization."""
    return {
        "Clerk-Authorization": "Bearer clerk-token-456",
        "Content-Type": "application/json",
    }


@pytest.fixture
def mock_headers_jsessionid():
    """Return mock headers with JSESSIONID."""
    return {
        "JSESSIONID": "session-id-789",
        "Content-Type": "application/json",
    }
