"""Tests for authentication backends."""

import time

import pytest
from mcp.server.auth.middleware.bearer_auth import AuthenticatedUser
from mcp.server.auth.provider import AccessToken
from starlette.authentication import AuthCredentials, AuthenticationError, SimpleUser
from starlette.responses import J<PERSON>NResponse

from nirvana_commons.auth.backends import (
    MultiHeaderAuthBackend,
    NirvanaAuthBackend,
    on_auth_error,
)


class MockHTTPConnection:
    """Mock HTTPConnection for testing."""

    def __init__(self, path="/", headers=None, method="GET") -> None:
        self.url = type("URL", (), {"path": path})()
        self.headers = headers or {}
        self.method = method
        self.state = type("State", (), {})()
        self.scope = {"method": method}


@pytest.mark.asyncio
async def test_nirvana_auth_backend_success(mocker, valid_access_token) -> None:
    """Test successful authentication with NirvanaAuthBackend."""
    mock_verifier = mocker.Mock()
    mock_verifier.verify_token = mocker.AsyncMock(return_value=valid_access_token)

    backend = NirvanaAuthBackend(
        paths_to_skip=["/health"],
        token_verifier=mock_verifier,
    )

    conn = MockHTTPConnection(
        path="/api/test",
        headers={"Authorization": "Bearer test-token"},
    )

    result = await backend.authenticate(conn)

    assert result is not None
    credentials, user = result
    assert isinstance(credentials, AuthCredentials)
    assert "authenticated" in credentials.scopes
    assert "nirvana:admin" in credentials.scopes
    # AuthenticatedUser stores the token in auth_info
    assert isinstance(user, AuthenticatedUser)
    assert user.access_token == valid_access_token

    mock_verifier.verify_token.assert_called_once_with("test-token")


@pytest.mark.asyncio
async def test_nirvana_auth_backend_skip_path(mocker) -> None:
    """Test that authentication is skipped for configured paths."""
    mock_verifier = mocker.Mock()

    backend = NirvanaAuthBackend(
        paths_to_skip=["/health", "/docs"],
        token_verifier=mock_verifier,
    )

    conn = MockHTTPConnection(path="/health")

    result = await backend.authenticate(conn)

    assert result is None
    mock_verifier.verify_token.assert_not_called()


@pytest.mark.asyncio
async def test_nirvana_auth_backend_no_header(mocker) -> None:
    """Test authentication failure when no Authorization header."""
    mock_verifier = mocker.Mock()

    backend = NirvanaAuthBackend(
        paths_to_skip=["/health"],
        token_verifier=mock_verifier,
    )

    conn = MockHTTPConnection(path="/api/test", headers={})

    with pytest.raises(AuthenticationError, match="Authorization header is required"):
        await backend.authenticate(conn)


@pytest.mark.asyncio
@pytest.mark.parametrize(
    ("auth_header", "error_match"),
    [
        ("just-a-token", "Invalid Authorization header format"),
        ("Bearer token extra", "Invalid Authorization header format"),
        ("Basic dXNlcjpwYXNz", "Invalid Authorization header format"),
        ("", "Authorization header is required"),
        (None, "Authorization header is required"),
    ],
)
async def test_nirvana_auth_backend_invalid_headers(
    mocker, auth_header, error_match
) -> None:
    """Test authentication failures with various invalid header formats."""
    mock_verifier = mocker.Mock()

    backend = NirvanaAuthBackend(
        paths_to_skip=["/health"],
        token_verifier=mock_verifier,
    )

    headers = {"Authorization": auth_header} if auth_header is not None else {}
    conn = MockHTTPConnection(path="/api/test", headers=headers)

    with pytest.raises(AuthenticationError, match=error_match):
        await backend.authenticate(conn)


@pytest.mark.asyncio
async def test_nirvana_auth_backend_expired_token(mocker) -> None:
    """Test authentication failure with expired token."""
    expired_token = AccessToken(
        token="expired-token",
        client_id="<EMAIL>",
        scopes=["authenticated"],
        expires_at=int(time.time()) - 3600,  # Expired 1 hour ago
    )

    mock_verifier = mocker.Mock()
    mock_verifier.verify_token = mocker.AsyncMock(return_value=expired_token)

    backend = NirvanaAuthBackend(
        paths_to_skip=["/health"],
        token_verifier=mock_verifier,
    )

    conn = MockHTTPConnection(
        path="/api/test",
        headers={"Authorization": "Bearer expired-token"},
    )

    result = await backend.authenticate(conn)

    assert result is None


@pytest.mark.asyncio
async def test_multi_header_backend_clerk_auth(mocker, valid_access_token) -> None:
    """Test MultiHeaderAuthBackend with Clerk-Authorization header."""
    mock_verifier = mocker.Mock()
    mock_verifier.verify_token = mocker.AsyncMock(return_value=valid_access_token)

    backend = MultiHeaderAuthBackend(
        accepted_headers=["Clerk-Authorization", "Authorization"],
        paths_to_skip=["/health"],
        token_verifier=mock_verifier,
    )

    conn = MockHTTPConnection(
        path="/api/test",
        headers={"Clerk-Authorization": "Bearer clerk-token"},
    )

    result = await backend.authenticate(conn)

    assert result is not None
    credentials, user = result
    assert isinstance(credentials, AuthCredentials)
    # AuthenticatedUser stores the token in auth_info
    assert isinstance(user, AuthenticatedUser)
    assert user.access_token == valid_access_token

    mock_verifier.verify_token.assert_called_once_with("clerk-token")


@pytest.mark.asyncio
async def test_multi_header_backend_jsessionid(mocker, valid_access_token) -> None:
    """Test MultiHeaderAuthBackend with JSESSIONID header."""
    mock_verifier = mocker.Mock()
    mock_verifier.verify_token = mocker.AsyncMock(return_value=valid_access_token)

    backend = MultiHeaderAuthBackend(
        accepted_headers=["Clerk-Authorization", "Authorization", "JSESSIONID"],
        paths_to_skip=["/health"],
        token_verifier=mock_verifier,
    )

    conn = MockHTTPConnection(
        path="/api/test",
        headers={"JSESSIONID": "session-123"},
    )

    result = await backend.authenticate(conn)

    assert result is not None
    mock_verifier.verify_token.assert_called_once_with("session-123")


@pytest.mark.asyncio
async def test_multi_header_backend_priority_order(mocker, valid_access_token) -> None:
    """Test that headers are checked in priority order."""
    mock_verifier = mocker.Mock()
    mock_verifier.verify_token = mocker.AsyncMock(return_value=valid_access_token)

    backend = MultiHeaderAuthBackend(
        accepted_headers=["Clerk-Authorization", "Authorization", "JSESSIONID"],
        paths_to_skip=["/health"],
        token_verifier=mock_verifier,
    )

    # Provide multiple headers - should use first in priority
    conn = MockHTTPConnection(
        path="/api/test",
        headers={
            "Authorization": "Bearer auth-token",
            "Clerk-Authorization": "Bearer clerk-token",
            "JSESSIONID": "session-123",
        },
    )

    result = await backend.authenticate(conn)

    assert result is not None
    # Should use Clerk-Authorization as it's first in priority
    mock_verifier.verify_token.assert_called_once_with("clerk-token")


@pytest.mark.asyncio
async def test_multi_header_backend_skip_options(mocker) -> None:
    """Test that OPTIONS requests are skipped."""
    mock_verifier = mocker.Mock()

    backend = MultiHeaderAuthBackend(
        accepted_headers=["Authorization"],
        paths_to_skip=["/health"],
        token_verifier=mock_verifier,
    )

    conn = MockHTTPConnection(
        path="/api/test",
        headers={},
        method="OPTIONS",
    )

    result = await backend.authenticate(conn)

    assert result is None
    mock_verifier.verify_token.assert_not_called()


@pytest.mark.asyncio
async def test_multi_header_backend_dev_fallback(mocker, valid_access_token) -> None:
    """Test development fallback token."""
    mock_verifier = mocker.Mock()
    mock_verifier.verify_token = mocker.AsyncMock(return_value=valid_access_token)

    backend = MultiHeaderAuthBackend(
        accepted_headers=["Authorization"],
        paths_to_skip=["/health"],
        token_verifier=mock_verifier,
        support_dev_fallback=True,
        dev_fallback_token="dev-token-123",
    )

    # No auth headers provided
    conn = MockHTTPConnection(path="/api/test", headers={})

    result = await backend.authenticate(conn)

    assert result is not None
    mock_verifier.verify_token.assert_called_once_with("dev-token-123")


@pytest.mark.asyncio
@pytest.mark.parametrize(
    ("headers", "should_fail", "test_description"),
    [
        ({"Authorization": "Bearer "}, True, "Empty bearer token"),
        ({"Authorization": "Basic dXNlcjpwYXNz"}, True, "Wrong auth scheme"),
        ({"JSESSIONID": ""}, True, "Empty JSESSIONID"),
        ({"JSESSIONID": "   "}, True, "Whitespace-only JSESSIONID"),
        ({"Authorization": "bearer valid-token"}, False, "Case-insensitive Bearer"),
        (
            {"JSESSIONID": "  session-with-spaces  "},
            False,
            "JSESSIONID with whitespace",
        ),
    ],
)
async def test_multi_header_backend_token_edge_cases(
    mocker, valid_access_token, headers, should_fail, test_description
) -> None:
    """Test various token edge cases including empty tokens, wrong schemes, and whitespace."""
    mock_verifier = mocker.Mock()
    mock_verifier.verify_token = mocker.AsyncMock(return_value=valid_access_token)

    backend = MultiHeaderAuthBackend(
        accepted_headers=["Authorization", "JSESSIONID"],
        paths_to_skip=[],
        token_verifier=mock_verifier,
        support_dev_fallback=False,
    )

    conn = MockHTTPConnection(path="/api/test", headers=headers)

    if should_fail:
        with pytest.raises(
            AuthenticationError,
            match="Authorization header with Bearer token is required",
        ):
            await backend.authenticate(conn)
    else:
        result = await backend.authenticate(conn)
        assert result is not None
        # Verify correct token extraction
        if "bearer valid-token" in str(headers.values()):
            mock_verifier.verify_token.assert_called_once_with("valid-token")
        elif "session-with-spaces" in str(headers.values()):
            mock_verifier.verify_token.assert_called_once_with("session-with-spaces")


@pytest.mark.asyncio
async def test_multi_header_backend_no_token_found(mocker) -> None:
    """Test error when no valid token is found."""
    mock_verifier = mocker.Mock()

    backend = MultiHeaderAuthBackend(
        accepted_headers=["Clerk-Authorization", "Authorization"],
        paths_to_skip=["/health"],
        token_verifier=mock_verifier,
        support_dev_fallback=False,
    )

    conn = MockHTTPConnection(path="/api/test", headers={})

    with pytest.raises(
        AuthenticationError, match="Authorization header with Bearer token is required"
    ):
        await backend.authenticate(conn)


@pytest.mark.asyncio
async def test_multi_header_backend_simple_user_fallback(mocker) -> None:
    """Test that SimpleUser is returned when no scopes."""
    simple_token = AccessToken(
        token="simple-token",
        client_id="<EMAIL>",
        scopes=["authenticated"],
        expires_at=None,
    )

    mock_verifier = mocker.Mock()
    mock_verifier.verify_token = mocker.AsyncMock(return_value=simple_token)

    backend = MultiHeaderAuthBackend(
        accepted_headers=["Authorization"],
        paths_to_skip=[],
        token_verifier=mock_verifier,
    )

    conn = MockHTTPConnection(
        path="/api/test",
        headers={"Authorization": "Bearer test-token"},
    )

    result = await backend.authenticate(conn)

    assert result is not None
    credentials, user = result
    assert credentials.scopes == ["authenticated"]
    # SimpleUser has username and display_name
    assert isinstance(user, SimpleUser)
    assert user.username == "<EMAIL>"
    assert user.display_name == "<EMAIL>"


def test_on_auth_error() -> None:
    """Test the auth error handler."""
    conn = MockHTTPConnection(path="/api/test")
    exc = AuthenticationError("Invalid token")

    response = on_auth_error(conn, exc)

    assert isinstance(response, JSONResponse)
    assert response.status_code == 401
    assert response.headers["WWW-Authenticate"] == "Bearer"
    assert response.body == b'{"detail":"Invalid token"}'
