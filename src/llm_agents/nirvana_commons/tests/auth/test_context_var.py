"""Tests for ContextVar functionality in middleware."""

import base64
import json

import pytest
from starlette.requests import Request
from starlette.responses import Response

from nirvana_commons.auth.middleware import (
    UserContextMiddleware,
    get_user_email,
    set_user_email,
)


@pytest.mark.asyncio
async def test_user_context_middleware_sets_context_var(mocker) -> None:
    """Test that UserContextMiddleware sets email in ContextVar."""
    # Create a mock request with authenticated user
    mock_request = mocker.Mock(spec=Request)
    mock_user = mocker.Mock()
    mock_user.username = "<EMAIL>"
    mock_request.user = mock_user

    # Mock request state
    mock_state = mocker.Mock()
    mock_request.state = mock_state
    mock_request.headers = {}

    # Clear any existing context
    set_user_email(None)
    assert get_user_email() is None

    # Track if context was set during request
    context_during_request = None

    async def mock_call_next(request):
        # Capture context during request processing
        nonlocal context_during_request
        context_during_request = get_user_email()
        return Response("OK")

    middleware = UserContextMiddleware(None)

    await middleware.dispatch(mock_request, mock_call_next)

    # Check that email was set in both places during request
    assert mock_request.state.user_email == "<EMAIL>"
    assert context_during_request == "<EMAIL>"

    # Check that context is cleared after request
    assert get_user_email() is None


@pytest.mark.asyncio
async def test_user_context_middleware_context_var_with_jwt_fallback(mocker) -> None:
    """Test ContextVar is set when using JWT fallback."""
    # Create a mock JWT
    payload = {"primary_email_address": "<EMAIL>"}
    payload_json = json.dumps(payload).encode()
    payload_b64 = base64.urlsafe_b64encode(payload_json).decode().rstrip("=")
    mock_jwt = f"header.{payload_b64}.signature"

    # Create a mock request without authenticated user
    mock_request = mocker.Mock(spec=Request)
    mock_request.headers = {"Clerk-Authorization": f"Bearer {mock_jwt}"}

    # Mock request state
    mock_state = mocker.Mock()
    mock_request.state = mock_state

    # Ensure no user attribute
    delattr(mock_request, "user")

    # Clear any existing context
    set_user_email(None)

    # Track context during request
    context_during_request = None

    async def mock_call_next(request):
        nonlocal context_during_request
        context_during_request = get_user_email()
        return Response("OK")

    middleware = UserContextMiddleware(None, extract_from_jwt=True)

    await middleware.dispatch(mock_request, mock_call_next)

    # Check both storage mechanisms
    assert mock_request.state.user_email == "<EMAIL>"
    assert context_during_request == "<EMAIL>"
    assert get_user_email() is None  # Cleared after request


def test_context_var_functions() -> None:
    """Test the context var getter and setter functions."""
    # Clear any existing context
    set_user_email(None)
    assert get_user_email() is None

    # Set an email
    set_user_email("<EMAIL>")
    assert get_user_email() == "<EMAIL>"

    # Update the email
    set_user_email("<EMAIL>")
    assert get_user_email() == "<EMAIL>"

    # Clear the email
    set_user_email(None)
    assert get_user_email() is None


@pytest.mark.asyncio
async def test_context_isolation_between_requests(mocker) -> None:
    """Test that context is properly isolated between concurrent requests."""
    import asyncio

    # Create two mock requests with different users
    def create_mock_request(email):
        mock_request = mocker.Mock(spec=Request)
        mock_user = mocker.Mock()
        mock_user.username = email
        mock_request.user = mock_user
        mock_state = mocker.Mock()
        mock_request.state = mock_state
        mock_request.headers = {}
        return mock_request

    request1 = create_mock_request("<EMAIL>")
    request2 = create_mock_request("<EMAIL>")

    middleware = UserContextMiddleware(None)

    # Track contexts during each request
    context1 = None
    context2 = None

    async def call_next_1(request):
        await asyncio.sleep(0.01)  # Simulate some async work
        nonlocal context1
        context1 = get_user_email()
        return Response("OK")

    async def call_next_2(request):
        nonlocal context2
        context2 = get_user_email()
        await asyncio.sleep(0.01)  # Simulate some async work
        return Response("OK")

    # Run both requests concurrently
    await asyncio.gather(
        middleware.dispatch(request1, call_next_1),
        middleware.dispatch(request2, call_next_2),
    )

    # Check that each request had its own context
    assert context1 == "<EMAIL>"
    assert context2 == "<EMAIL>"

    # Check that context is cleared after both requests
    assert get_user_email() is None
