"""Tests for middleware helpers."""

import base64
import json

import pytest
from starlette.requests import Request
from starlette.responses import Response

from nirvana_commons.auth.middleware import (
    UserContextMiddleware,
    extract_email_from_jwt,
)


@pytest.mark.parametrize(
    ("payload", "expected_email", "description"),
    [
        (
            {"primary_email_address": "<EMAIL>", "sub": "123"},
            "<EMAIL>",
            "Clerk format with primary_email_address",
        ),
        (
            {"email": "<EMAIL>", "sub": "456"},
            "<EMAIL>",
            "Standard format with email",
        ),
        (
            {
                "primary_email_address": "<EMAIL>",
                "email": "<EMAIL>",
            },
            "<EMAIL>",
            "Both fields present - primary takes precedence",
        ),
        (
            {"sub": "789", "name": "Test User"},
            None,
            "No email fields",
        ),
        (
            {"primary_email_address": "", "email": ""},
            "",  # Empty string is returned, not None
            "Empty email values",
        ),
    ],
)
def test_extract_email_from_jwt_scenarios(payload, expected_email, description) -> None:
    """Test email extraction from various JWT payload scenarios."""
    payload_json = json.dumps(payload)
    payload_b64 = base64.urlsafe_b64encode(payload_json.encode()).decode().rstrip("=")
    token = f"header.{payload_b64}.signature"

    email = extract_email_from_jwt(token)
    assert email == expected_email


@pytest.mark.parametrize(
    ("token", "description"),
    [
        ("not-a-jwt", "Invalid format"),
        ("", "Empty token"),
        ("only.two", "Missing signature part"),
        ("header.invalid-base64!@#.signature", "Invalid base64"),
    ],
)
def test_extract_email_from_jwt_invalid_tokens(token, description) -> None:
    """Test extracting email from various invalid JWT tokens."""
    assert extract_email_from_jwt(token) is None


@pytest.mark.asyncio
async def test_user_context_middleware_with_authenticated_user(mocker) -> None:
    """Test UserContextMiddleware with authenticated user."""

    # Create a mock state object
    class MockState:
        pass

    mock_state = MockState()

    # Mock request with authenticated user
    mock_user = mocker.Mock()
    mock_user.username = "<EMAIL>"

    mock_request = mocker.Mock(spec=Request)
    mock_request.user = mock_user
    mock_request.state = mock_state

    # Mock call_next
    async def mock_call_next(request):
        return Response("OK")

    middleware = UserContextMiddleware(None)

    await middleware.dispatch(mock_request, mock_call_next)

    # Check that email was stored in request state
    assert mock_request.state.user_email == "<EMAIL>"


@pytest.mark.asyncio
async def test_user_context_middleware_with_mcp_user(mocker) -> None:
    """Test UserContextMiddleware with MCP AuthenticatedUser."""

    # Create a mock state object
    class MockState:
        pass

    mock_state = MockState()

    # Mock request with MCP authenticated user
    mock_access_token = mocker.Mock()
    mock_access_token.client_id = "<EMAIL>"

    mock_user = mocker.Mock(
        spec=["access_token"]
    )  # Only has access_token, not username
    mock_user.access_token = mock_access_token

    mock_request = mocker.Mock(spec=Request)
    mock_request.user = mock_user
    mock_request.state = mock_state
    mock_request.headers = {}  # No headers to fall back on

    # Mock call_next
    async def mock_call_next(request):
        return Response("OK")

    middleware = UserContextMiddleware(None)

    await middleware.dispatch(mock_request, mock_call_next)

    # Check that email was stored in request state
    assert mock_request.state.user_email == "<EMAIL>"


@pytest.mark.asyncio
async def test_user_context_middleware_jwt_fallback(mocker) -> None:
    """Test UserContextMiddleware falling back to JWT extraction."""
    # Create a mock JWT
    payload = {"primary_email_address": "<EMAIL>"}
    payload_json = json.dumps(payload)
    payload_b64 = base64.urlsafe_b64encode(payload_json.encode()).decode().rstrip("=")
    token = f"header.{payload_b64}.signature"

    # Create a mock state object
    class MockState:
        pass

    mock_state = MockState()

    # Mock request without authenticated user
    mock_request = mocker.Mock(spec=Request)
    mock_request.user = None  # No authenticated user
    mock_request.headers = {"Clerk-Authorization": f"Bearer {token}"}
    mock_request.state = mock_state

    # Mock call_next
    async def mock_call_next(request):
        return Response("OK")

    middleware = UserContextMiddleware(None, extract_from_jwt=True)

    await middleware.dispatch(mock_request, mock_call_next)

    # Check that email was extracted from JWT
    assert mock_request.state.user_email == "<EMAIL>"


@pytest.mark.asyncio
async def test_user_context_middleware_no_jwt_extraction(mocker) -> None:
    """Test UserContextMiddleware with JWT extraction disabled."""

    # Create a mock state object
    class MockState:
        pass

    mock_state = MockState()

    # Mock request without authenticated user
    mock_request = mocker.Mock(spec=Request)
    mock_request.user = None
    mock_request.headers = {"Authorization": "Bearer some-token"}
    mock_request.state = mock_state

    # Mock call_next
    async def mock_call_next(request):
        return Response("OK")

    middleware = UserContextMiddleware(None, extract_from_jwt=False)

    await middleware.dispatch(mock_request, mock_call_next)

    # Check that email is None
    assert mock_request.state.user_email is None


@pytest.mark.skip(reason="OpenTelemetry import is optional and hard to mock")
@pytest.mark.asyncio
async def test_user_context_middleware_opentelemetry(mocker) -> None:
    """Test UserContextMiddleware with OpenTelemetry integration."""

    # Create a mock state object
    class MockState:
        pass

    mock_state = MockState()

    # Mock OpenTelemetry
    mock_span = mocker.Mock()
    mock_trace = mocker.Mock()
    mock_trace.get_current_span = mocker.Mock(return_value=mock_span)

    # Patch the trace module directly in the middleware module
    with mocker.patch("nirvana_commons.auth.middleware.trace", mock_trace):
        # Mock request with authenticated user
        mock_user = mocker.Mock()
        mock_user.username = "<EMAIL>"

        mock_request = mocker.Mock(spec=Request)
        mock_request.user = mock_user
        mock_request.state = mock_state

        # Mock call_next
        async def mock_call_next(request):
            return Response("OK")

        middleware = UserContextMiddleware(None)

        await middleware.dispatch(mock_request, mock_call_next)

        # Check that span attribute was set
        mock_span.set_attribute.assert_called_once_with(
            "user.email", "<EMAIL>"
        )


@pytest.mark.asyncio
async def test_user_context_middleware_no_user(mocker) -> None:
    """Test UserContextMiddleware with no user information."""

    # Create a mock state object
    class MockState:
        pass

    mock_state = MockState()

    # Mock request without any user info
    mock_request = mocker.Mock(spec=Request)
    mock_request.state = mock_state
    mock_request.headers = {}

    # Ensure no user attribute
    delattr(mock_request, "user")

    # Mock call_next
    async def mock_call_next(request):
        return Response("OK")

    middleware = UserContextMiddleware(None)

    await middleware.dispatch(mock_request, mock_call_next)

    # Check that email is None
    assert mock_request.state.user_email is None
