"""Tests for token verifiers."""

import pytest
from mcp import <PERSON>c<PERSON><PERSON><PERSON><PERSON>
from mcp.types import INTERNAL_ERROR, PARSE_ERROR
from starlette.authentication import AuthenticationError

from nirvana_commons.auth.verifiers import NirvanaTokenVerifier
from nirvana_rest_api.models.roles import Roles
from nirvana_rest_api.types import UNSET, Response


@pytest.mark.asyncio
async def test_verify_token_success(mocker, valid_token) -> None:
    """Test successful token verification."""
    # Mock the API response
    mock_response = mocker.Mock(spec=Response)
    mock_response.status_code = 200
    # Mock the parsed response
    mock_user_info = mocker.Mock()
    mock_user_info.email = "<EMAIL>"
    mock_user_info.id = "user-123"

    # Mock roles
    mock_roles = mocker.Mock()
    mock_roles.nirvana_roles = [mocker.Mock(role="admin")]
    mock_roles.agency_roles = [mocker.Mock(role="viewer")]
    mock_roles.fleet_roles = UNSET
    mock_user_info.roles = mock_roles

    mock_response.parsed = mock_user_info

    # Mock the get_me function
    mock_get_me = mocker.patch(
        "nirvana_commons.auth.verifiers.get_me",
        return_value=mock_response,
    )

    # Mock the AuthenticatedClient
    mock_client = mocker.Mock()
    mock_authenticated_client = mocker.patch(
        "nirvana_commons.auth.verifiers.AuthenticatedClient"
    )
    mock_authenticated_client.return_value.__aenter__.return_value = mock_client
    mock_authenticated_client.return_value.__aexit__.return_value = None

    verifier = NirvanaTokenVerifier()
    result = await verifier.verify_token(valid_token)

    assert result is not None
    assert result.token == valid_token
    assert result.client_id == "<EMAIL>"
    assert "authenticated" in result.scopes
    assert "nirvana:admin" in result.scopes
    assert "agency:viewer" in result.scopes
    assert result.expires_at is None

    # Verify API was called correctly
    mock_authenticated_client.assert_called_once_with(
        base_url="https://api.prod.nirvanatech.com",
        token=valid_token,
        auth_header_name="Clerk-Authorization",
        prefix="Bearer",
    )
    mock_get_me.assert_called_once_with(client=mock_client)


@pytest.mark.asyncio
@pytest.mark.parametrize(
    ("token", "error_match"),
    [
        ("", "No token provided"),
        (None, "No token provided"),
        ("   ", "No token provided"),
    ],
)
async def test_verify_token_empty_variations(token, error_match) -> None:
    """Test verification with various empty token scenarios."""
    verifier = NirvanaTokenVerifier()

    with pytest.raises(AuthenticationError, match=error_match):
        await verifier.verify_token(token)


@pytest.mark.asyncio
@pytest.mark.parametrize(
    ("status_code", "parsed", "expected_exception", "error_code", "error_match"),
    [
        (401, None, AuthenticationError, None, "Invalid or expired token"),
        (403, None, AuthenticationError, None, "Invalid or expired token"),
        (500, None, McpError, INTERNAL_ERROR, "500 error"),
        (503, None, McpError, INTERNAL_ERROR, "503 error"),
        (200, None, McpError, PARSE_ERROR, "Failed to parse"),
    ],
)
async def test_verify_token_api_errors(
    mocker, status_code, parsed, expected_exception, error_code, error_match
) -> None:
    """Test handling of various API response errors."""
    # Mock the API response
    mock_response = mocker.Mock(spec=Response)
    mock_response.status_code = status_code
    mock_response.parsed = parsed

    mocker.patch("nirvana_commons.auth.verifiers.get_me", return_value=mock_response)
    mock_authenticated_client = mocker.patch(
        "nirvana_commons.auth.verifiers.AuthenticatedClient"
    )
    mock_authenticated_client.return_value.__aenter__.return_value = mocker.Mock()
    mock_authenticated_client.return_value.__aexit__.return_value = None

    verifier = NirvanaTokenVerifier()

    if expected_exception == McpError:
        with pytest.raises(McpError) as exc_info:
            await verifier.verify_token("test-token")
        assert exc_info.value.error.code == error_code
        assert error_match in exc_info.value.error.message
    else:
        with pytest.raises(expected_exception, match=error_match):
            await verifier.verify_token("test-token")


@pytest.mark.asyncio
async def test_verify_token_caching(mocker, valid_token) -> None:
    """Test that token verification results are cached."""
    # Mock the API response
    mock_response = mocker.Mock(spec=Response)
    mock_response.status_code = 200
    # Mock the parsed response
    mock_user_info = mocker.Mock()
    mock_user_info.email = "<EMAIL>"
    mock_user_info.id = "user-123"
    mock_user_info.roles = UNSET
    mock_response.parsed = mock_user_info

    mock_get_me = mocker.patch(
        "nirvana_commons.auth.verifiers.get_me",
        return_value=mock_response,
    )
    mock_authenticated_client = mocker.patch(
        "nirvana_commons.auth.verifiers.AuthenticatedClient"
    )
    mock_authenticated_client.return_value.__aenter__.return_value = mocker.Mock()
    mock_authenticated_client.return_value.__aexit__.return_value = None

    verifier = NirvanaTokenVerifier()

    # First call
    result1 = await verifier.verify_token(valid_token)
    assert result1.client_id == "<EMAIL>"

    # Second call - should use cache
    result2 = await verifier.verify_token(valid_token)
    assert result2.client_id == "<EMAIL>"

    # API should only be called once due to caching
    assert mock_get_me.call_count == 1


@pytest.mark.asyncio
async def test_extract_scopes_from_roles(mocker) -> None:
    """Test scope extraction from various role configurations."""
    verifier = NirvanaTokenVerifier()

    # Test with all role types
    roles = Roles(
        nirvana_roles=[
            mocker.Mock(role="admin"),
            mocker.Mock(role="user"),
        ],
        agency_roles=[mocker.Mock(role="viewer")],
        fleet_roles=[mocker.Mock(role="operator")],
    )

    scopes = verifier._extract_scopes_from_roles(roles)

    assert "authenticated" in scopes
    assert "nirvana:admin" in scopes
    assert "nirvana:user" in scopes
    assert "agency:viewer" in scopes
    assert "fleet:operator" in scopes

    # Test with UNSET roles
    scopes_empty = verifier._extract_scopes_from_roles(UNSET)
    assert scopes_empty == ["authenticated"]


@pytest.mark.asyncio
async def test_verify_token_custom_base_url(mocker, valid_token) -> None:
    """Test token verification with custom base URL."""
    # Mock the API response
    mock_response = mocker.Mock(spec=Response)
    mock_response.status_code = 200
    # Mock the parsed response
    mock_user_info = mocker.Mock()
    mock_user_info.email = "<EMAIL>"
    mock_user_info.id = "user-123"
    mock_user_info.roles = UNSET
    mock_response.parsed = mock_user_info

    mocker.patch("nirvana_commons.auth.verifiers.get_me", return_value=mock_response)

    mock_authenticated_client = mocker.patch(
        "nirvana_commons.auth.verifiers.AuthenticatedClient"
    )
    mock_authenticated_client.return_value.__aenter__.return_value = mocker.Mock()
    mock_authenticated_client.return_value.__aexit__.return_value = None

    custom_url = "https://api.staging.nirvanatech.com"
    verifier = NirvanaTokenVerifier(nirvana_api_base_url=custom_url)

    await verifier.verify_token(valid_token)

    # Verify custom URL was used
    mock_authenticated_client.assert_called_once_with(
        base_url=custom_url,
        token=valid_token,
        auth_header_name="Clerk-Authorization",
        prefix="Bearer",
    )
