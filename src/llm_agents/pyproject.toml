[project]
name = "llm-agents"
version = "0.1.0"
readme = "README.md"
requires-python = ">=3.11"
dependencies = []

[dependency-groups]
dev = [
    "poethepoet>=0.24.0",
    "mypy>=1.15.0",
    "pytest>=8.3.5",
    "pytest-asyncio>=0.26.0",
    "pytest-mock>=3.14.0",
    "ruff>=0.11.8",
    "types-colorama>=0.4.15.20240311",
    "types-python-dateutil>=2.9.0.20241206",
    "types-pytz>=2025.2.0.20250326",
    "types-requests>=2.32.0.20250328",
    "testcontainers[redis]>=4.10.0",
    "types-pyyaml>=6.0.12.20250402",
    "types-redis>=*******",
    "sqlacodegen>=3.0.0",
    "pydantic>=2.6.0",
    "pydantic-settings>=2.2.0",
    "fastapi[standard]>=0.110.0",
    "uvicorn",
    "injector>=0.21.0",
    "fastapi-injector>=0.5.0",
    "httpx>=0.26.0",
    "loguru>=0.7.2",
    "temporalio>=1.13.0",
    "pyperclip>=1.8.0",  # Clipboard support for token script
    "playwright>=1.44.0",  # Headful browser automation for Clerk token retrieval
]

[tool.uv.workspace]
members = ["claims_agent", "mcp_servers", "nirvana_rest_api", "nirvana_commons"]

[tool.mypy]
python_version = "3.11"
packages = ["claims_agent", "mcp_servers", "nirvana_commons"]
mypy_path = "claims_agent/src:mcp_servers/src:nirvana_commons/src"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = false
no_implicit_optional = true
strict_optional = true
warn_redundant_casts = true
warn_unused_ignores = false
warn_no_return = true
warn_unreachable = false
exclude = [
    "nirvana_rest_api/.*",
    "scripts/clean.py",
]

# External dependencies without type stubs
[[tool.mypy.overrides]]
module = [
    "mcp.*",
    "nirvana_rest_api.*",
    "llama_index.*",
    "langchain_mcp_adapters.*",
    "llama_cloud_services",
    "llama_cloud_services.*",
    "arize",
    "arize.*",
]
ignore_missing_imports = true

# Test files - relaxed type checking for test-specific patterns
[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false
disallow_incomplete_defs = false
disallow_untyped_decorators = false
warn_return_any = false

[[tool.mypy.overrides]]
module = [
    "async_lru",
    "fastapi_injector",
    "langchain_mcp_adapters.*",
]
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = [
    "langchain",
    "langchain.*",
    "langchain_core",
    "langchain_core.*",
    "langchain_openai",
    "langchain_openai.*",
    "opentelemetry",
    "opentelemetry.*",
    "sentry_sdk",
    "extend_ai",
]
ignore_missing_imports = true

[tool.ruff]
target-version = "py311"
line-length = 120
extend-exclude = ["nirvana_rest_api", "scripts/clean.py"]

[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "F",  # Pyflakes
    "I",  # isort
    "N",  # pep8-naming
    "W",  # pycodestyle warnings
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
    "C90",# mccabe complexity
    "A",  # flake8-builtins
    "D",  # pydocstyle
    "SIM",# flake8-simplify
    "PT", # flake8-pytest-style
    "S",  # flake8-bandit (security)
    "ANN", # flake8-annotations
    "FBT", # flake8-bugbear-annotations
    "COM", # flake8-commas
    "RET", # flake8-return
    "TID", # flake8-tidy-imports
]
ignore = ["ANN401", "C408", "W293", "W291", "COM812"]

[tool.ruff.lint.per-file-ignores]
"tests/**/*.py" = ["S101"]  # Allow assert statements in tests

# pydocstyle configuration
[tool.ruff.lint.pydocstyle]
convention = "google"

# isort configuration
[tool.ruff.lint.isort]
case-sensitive = true
combine-as-imports = false
lines-after-imports = 2
section-order = ["future", "standard-library", "third-party", "first-party", "local-folder"]

# Format configuration
[tool.ruff.format]
quote-style = "double"
indent-style = "space"
line-ending = "auto"

# pytest configuration
[tool.pytest.ini_options]
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "function"
testpaths = ["claims_agent/tests", "mcp_servers/tests", "nirvana_commons/tests"]
python_files = "test_*.py"
python_functions = "test_*"
markers = [
    "unit: marks tests as unit tests",
    "integration: marks tests as integration tests",
    "temporal: marks tests as temporal tests",
]
# We disable the following warnings from imported libraries
filterwarnings = [
    # 1. PydanticDeprecatedSince20 raised by pydantic
    # pydantic/_internal/_generate_schema.py:293: PydanticDeprecatedSince20: `json_encoders` is deprecated.
    # See https://docs.pydantic.dev/2.11/concepts/serialization/#custom-serializers for alternatives.
    # Deprecated in Pydantic V2.0 to be removed in V3.0.
    # See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.11/migration/
    "ignore::pydantic.PydanticDeprecatedSince20:pydantic.*",

    # 2. PydanticDeprecatedSince20 raised by temporalio
    # /temporalio/converter.py:509: PydanticDeprecatedSince20: The `dict` method is deprecated; use `model_dump` instead.
    # Deprecated in Pydantic V2.0 to be removed in V3.0.
    # See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.11/migration/
    # "ignore::pydantic.PydanticDeprecatedSince20:temporalio.*",

    # 3. PendingDeprecationWarning raised by starlette
    # starlette/formparsers.py:12: PendingDeprecationWarning: Please use `import python_multipart` instead.
    "ignore::PendingDeprecationWarning:starlette.*",

    # 4. UserWarning raised by temporalio
    # temporalio/converter.py:566: UserWarning: If you're using Pydantic v2, use
    # temporalio.contrib.pydantic.pydantic_data_converter.
    # If you're using Pydantic v1 and cannot upgrade, refer to
    # https://github.com/temporalio/samples-python/tree/main/pydantic_converter_v1
    # for better v1 support.
    "ignore::UserWarning:temporalio.*",
]

[tool.poe.tasks]
# Global tasks
format = { cmd = "ruff format ." }
lint = { cmd = "ruff check ." }
lint-fix = { cmd = "ruff check --fix ." }
typecheck = { cmd = "mypy --explicit-package-bases" }
install-local = { cmd = "uv pip install -e claims_agent -e mcp_servers -e nirvana_rest_api -e nirvana_commons --quiet" }
test = { sequence = ["install-local", { cmd = "pytest -m 'not temporal'" }], env = { LLAMA_CLOUD_API_KEY = "DUMMY_KEY_FOR_TESTS", SUPABASE_DB_URL = "************************************", TEMPORAL_SERVER_URL = "dummy:7233" } }
test-temporal = { sequence = ["install-local", { cmd = "pytest -m temporal" }], env = { LLAMA_CLOUD_API_KEY = "DUMMY_KEY_FOR_TESTS", SUPABASE_DB_URL = "************************************", TEMPORAL_SERVER_URL = "dummy:7233" } }
test-all = { sequence = ["install-local", { cmd = "pytest" }], env = { LLAMA_CLOUD_API_KEY = "DUMMY_KEY_FOR_TESTS", SUPABASE_DB_URL = "************************************", TEMPORAL_SERVER_URL = "dummy:7233" } }
check = { sequence = ["format", "lint", "typecheck"] }
fix-all = { sequence = ["format", "lint-fix"] }

# Generate ORM models
[tool.poe.tasks.generate-orm]
sequence = [
    "generate-orm-claims-agent",
    "fix-all",
]

[tool.poe.tasks.generate-orm-claims-agent]
help = "Generate ORM models for the claims agent"
cwd = "claims_agent"
cmd = "uv run poe generate-orm"

# Generation tasks
[tool.poe.tasks.generate-api-client]
help = "Generate OpenAPI specification for the claims agent"
cwd = "claims_agent"
cmd = "uv run poe generate-openapi"

[tool.poe.tasks.start-dev]
help = "Start all development services (claims_agent, mcp_servers) using Overmind and Procfile."
cmd = "overmind start -f Procfile"

# Clean task (now points to an external script)
[tool.poe.tasks.clean]
script = "poethepoet.scripts:rm('**/__pycache__', '**/.pytest_cache', '**/.mypy_cache', '**/.ruff_cache', '**/build', '**/dist', '**/*.pyc', '**/*.pyo', '**/*.pyd')"

# Docker tasks
[tool.poe.tasks.build-image]
help = "Build Docker image for a package (does not use docker-compose)"
args = [{name = "package", help = "Package to build"}, {name = "tag", help = "Image tag", default = "latest"}]
shell = "docker buildx build -f $package/Dockerfile -t $package:$tag ."

# Renamed from run-docker / run-single-image
[tool.poe.tasks.compose-start-service]
help = "Build (if needed) and start a specific service defined in docker-compose.yml in detached mode."
args = [{name = "service", help = "Name of the service to start (e.g., claims_agent, mcp_servers)"}]
shell = "COMPOSE_BAKE=true docker compose up --build -d $service"

[tool.poe.tasks.compose-up]
help = "Build and start all services defined in docker-compose.yml in detached mode."
shell = "COMPOSE_BAKE=true docker compose up --build -d"

[tool.poe.tasks.compose-down]
help = "Stop and remove containers, networks, images, and volumes created by compose-up."
cmd = "docker compose down"

[tool.poe.tasks.compose-logs]
help = "Follow log output from services."
cmd = "docker compose logs -f"

[tool.poe.tasks.build-and-push]
help = "Build and push Docker image to ECR"
args = [
    {name = "package", help = "Package to build and push"},
    {name = "tag", help = "Image tag"},
    {name = "aws_account_id", help = "AWS account ID", default = "************"},
    {name = "aws_region", help = "AWS region", default = "us-east-2"},
    {name = "ecr_path", help = "ECR path"}
]
shell = '''
docker buildx build --platform linux/amd64 \
  -f $package/Dockerfile \
  -t "$aws_account_id.dkr.ecr.$aws_region.amazonaws.com/$ecr_path:$tag" \
  .
docker push "$aws_account_id.dkr.ecr.$aws_region.amazonaws.com/$ecr_path:$tag"
'''

# ---------------------------
# Developer utilities
# ---------------------------
[tool.poe.tasks.get-clerk-token]
help = "Interactive helper to obtain a long-lived Clerk JWT via Google sign-in."
cmd = "uv run python scripts/get_clerk_token.py" 

[tool.poe.tasks.supabase-migrations]
help = "Supabase migrations"
cmd = "uv run python scripts/supabase_migrations.py"
