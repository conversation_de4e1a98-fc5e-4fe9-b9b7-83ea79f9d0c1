package backfill_forms_v2_db

import (
	"nirvanatech.com/nirvana/forms/model"
)

var FormCodeToFieldConfigs = map[string]map[string]model.Field{
	"CAP 70 09 00 NF 01 22": {
		"PolicyNumber": PolicyNumberFieldAL,
		"Company":      InsuranceCarrierOnFormFooterField,
	},
	"CAP 04 02 00 NF 11 23": {
		"PolicyNumber": PolicyNumberFieldAL,
		"Company":      InsuranceCarrierOnFormFooterField,
	},
	"CAP 70 90 00 NF 11 23": {
		"PolicyNumber": PolicyNumberFieldAL,
		"Company":      InsuranceCarrierOnFormFooterField,
	},
	"CAP 71 02 00 NF 01 22": {
		"PolicyNumber": PolicyNumberFieldAL,
		"Company":      InsuranceCarrierOnFormFoot<PERSON><PERSON>ield,
	},
	"CAP 70 84 00 NF 11 23": {
		"PolicyNumber": PolicyNumberFieldAL,
		"Company":      InsuranceCarrierOnFormFooterField,
	},
	"CAP 70 85 00 NF 11 23": {
		"PolicyNumber": PolicyNumberFieldAL,
		"Company":      InsuranceCarrierOnFormFooterField,
	},
	// TODO: Fix these fill templates to populate all fields for the forms.
	"NIS IM 025 05 25": {
		"PolicyNumber": PolicyNumberFieldMTC,
		"Company":      InsuranceCarrierOnFormFooterField,
	},
	"NIS IM 020 05 25": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"NIS IM DS 001 05 25": {
		"PolicyNumber":               PolicyNumberFieldMTC,
		"Company":                    InsuranceCarrierOnFormFooterField,
		"EarnedChargesLimit":         CoverageEarnedFreightLimitField,
		"PolicyPeriodFrom":           PolicyEffectiveDateField,
		"PolicyPeriodTo":             PolicyExpirationDateField,
		"NamedInsured":               InsuredNamePlusDBAField,
		"InsuredMailingAddress":      PhysicalAddressField,
		"PollutantCleanUpandRemoval": CoveragePollutantCleanupAndRemovalLimitField,
		"CargoHandlingEquipment":     CoverageMiscellaneousEquipmentLimitField,
		"RemovalExpenses":            CoverageDebrisRemovalLimitField,
		"TerminalSchedule":           ComputedReferToTerminalScheduleField,
		"CargoRate":                  MTCPremiumPerHundredMilesField,
		"CargoTotalPremium":          MTCPremiumField,
		"CargoLimit":                 MTCLimitField,
		"CargoDeductible":            MTCDeductibleField,
		"Date":                       Date,
		"LossMitigationExpenses":     CoverageLossMitigationExpensesLimitField,
	},
	"NIS IM 022 05 25": {
		"Company":                 InsuranceCarrierOnFormFooterField,
		"PolicyNumber":            PolicyNumberFieldMTC,
		"CargoTILimitOfInsurance": CargoTILimitOfInsuranceField,
		"CargoTIDeductible":       CargoTIDeductibleField,
	},
	"NIS IM 023 05 25": {
		"Company":                  InsuranceCarrierOnFormFooterField,
		"PolicyNumber":             PolicyNumberFieldMTC,
		"NamedInsured":             InsuredNamePlusDBAField,
		"EndorsementEffectiveDate": PolicyEffectiveDateField,
	},
	"NIS IM 024 05 25": {
		"PolicyNumber": PolicyNumberFieldMTC,
		"Company":      InsuranceCarrierOnFormFooterField,
	},
	"NIS IM 026 05 25": {
		"PolicyNumber":                    PolicyNumberFieldMTC,
		"Company":                         InsuranceCarrierOnFormFooterField,
		"ReeferLimitPerVehicle":           ReeferLimitPerVehicleField,
		"ReeferCoverageScheduledTerminal": ComputedReferToTerminalScheduleField,
		"ReeferBreakdownDeductible":       ReeferBreakdownDeductibleField,
		"ReeferBreakdown":                 ReeferBreakdownField,
		"ReeferBreakdownWithHumanError":   ReeferBreakdownWithHumanErrorField,
	},
	"NIS IM 028 05 25": {
		"PolicyNumber":     PolicyNumberFieldMTC,
		"Company":          InsuranceCarrierOnFormFooterField,
		"PolicyPeriodFrom": PolicyEffectiveDateField,
		"PolicyPeriodTo":   PolicyExpirationDateField,
		"NamedInsured":     InsuredNamePlusDBAField,
	},
	"NIS IM 029 05 25": {
		"PolicyNumber": PolicyNumberFieldMTC,
		"Company":      InsuranceCarrierOnFormFooterField,
	},
	"NIS IM 030 05 25": {
		"PolicyNumber": PolicyNumberFieldMTC,
		"Company":      InsuranceCarrierOnFormFooterField,
	},
	"NIS IM 031 05 25": {
		"PolicyNumber": PolicyNumberFieldMTC,
		"Company":      InsuranceCarrierOnFormFooterField,
	},
	"NIS IM 032 05 25": {
		"PolicyNumber": PolicyNumberFieldMTC,
		"Company":      InsuranceCarrierOnFormFooterField,
	},
	"NIS IM 033 05 25": {
		"PolicyNumber":             PolicyNumberFieldMTC,
		"Company":                  InsuranceCarrierOnFormFooterField,
		"NamedInsured":             InsuredNamePlusDBAField,
		"EndorsementEffectiveDate": PolicyEffectiveDateField,
		"BlanketApplies":           BlanketAppliesField,
	},
	"NIS IM 034 05 25": {
		"PolicyNumber":             PolicyNumberFieldMTC,
		"Company":                  InsuranceCarrierOnFormFooterField,
		"NamedInsured":             InsuredNamePlusDBAField,
		"EndorsementEffectiveDate": PolicyEffectiveDateField,
		"BlanketApplies":           BlanketAppliesField,
		"PolicyPeriodTo":           PolicyExpirationDateField,
		"PolicyPeriodFrom":         PolicyEffectiveDateField,
	},
	"NIS IM 035 05 25": {
		"PolicyNumber":             PolicyNumberFieldMTC,
		"Company":                  InsuranceCarrierOnFormFooterField,
		"NamedInsured":             InsuredNamePlusDBAField,
		"EndorsementEffectiveDate": PolicyEffectiveDateField,
		"PolicyPeriodTo":           PolicyExpirationDateField,
	},
	"NIS IM 036 05 25": {
		"PolicyNumber":                   PolicyNumberFieldMTC,
		"Company":                        InsuranceCarrierOnFormFooterField,
		"NamedInsured":                   InsuredNamePlusDBAField,
		"PolicyPeriodFrom":               PolicyEffectiveDateField,
		"PolicyPeriodTo":                 PolicyExpirationDateField,
		"NumberOfDaysNoticeCancellation": NumberOfDaysNoticeCancellationField,
		"NumberOfDaysNoticeNonpayment":   NumberOfDaysNoticeNonpaymentField,
	},
	"NIS IM 038 05 25": {
		"PolicyNumber": PolicyNumberFieldMTC,
		"Company":      InsuranceCarrierOnFormFooterField,
	},
	"NIS IM 021 AZ 05 25": {
		"PolicyNumber": PolicyNumberFieldMTC,
		"Company":      InsuranceCarrierOnFormFooterField,
	},
	"NIS IM 021 KY 05 25": {
		"PolicyNumber": PolicyNumberFieldMTC,
		"Company":      InsuranceCarrierOnFormFooterField,
	},
	"NIS IM 021 MI 05 25": {
		"PolicyNumber": PolicyNumberFieldMTC,
		"Company":      InsuranceCarrierOnFormFooterField,
	},
	"NIS IM 021 MO 05 25": {
		"PolicyNumber": PolicyNumberFieldMTC,
		"Company":      InsuranceCarrierOnFormFooterField,
	},
	"NIS IM 021 NE 05 25": {
		"PolicyNumber": PolicyNumberFieldMTC,
		"Company":      InsuranceCarrierOnFormFooterField,
	},
	"NIS IM 021 NM 05 25": {
		"PolicyNumber": PolicyNumberFieldMTC,
		"Company":      InsuranceCarrierOnFormFooterField,
	},
	"NIS IM 021 OR 05 25": {
		"PolicyNumber": PolicyNumberFieldMTC,
		"Company":      InsuranceCarrierOnFormFooterField,
	},
	"NIS IM 021 WA 05 25": {
		"PolicyNumber": PolicyNumberFieldMTC,
		"Company":      InsuranceCarrierOnFormFooterField,
	},
	"NIS IM 021 WI 05 25": {
		"PolicyNumber": PolicyNumberFieldMTC,
		"Company":      InsuranceCarrierOnFormFooterField,
	},
	"NIS IM N 001 05 25": {
		"PolicyNumber": PolicyNumberFieldMTC,
		"Company":      InsuranceCarrierOnFormFooterField,
	},
	"NIS IM DS 001 CA 05 25": {
		"PolicyNumber":               PolicyNumberFieldMTC,
		"Company":                    InsuranceCarrierOnFormFooterField,
		"EarnedChargesLimit":         CoverageEarnedFreightLimitField,
		"PolicyPeriodFrom":           PolicyEffectiveDateField,
		"PolicyPeriodTo":             PolicyExpirationDateField,
		"NamedInsured":               InsuredNamePlusDBAField,
		"InsuredMailingAddress":      PhysicalAddressField,
		"PollutantCleanUpandRemoval": CoveragePollutantCleanupAndRemovalLimitField,
		"CargoHandlingEquipment":     CoverageMiscellaneousEquipmentLimitField,
		"RemovalExpenses":            CoverageDebrisRemovalLimitField,
		"TerminalSchedule":           ComputedReferToTerminalScheduleField,
		"CargoRate":                  MTCPremiumPerHundredMilesField,
		"CargoTotalPremium":          MTCPremiumField,
		"CargoLimit":                 MTCLimitField,
		"CargoDeductible":            MTCDeductibleField,
		"CASLTaxMTC":                 MTCSurplusLinesTax,
		"CASLStampingFeeMTC":         MTCStampingFee,
		"Date":                       Date,
		"Broker":                     BrokerField,
		"BrokerMailingAddress":       BrokerMailingAddressField,
		"LossMitigationExpenses":     CoverageLossMitigationExpensesLimitField,
	},
	"NDCPO A 06 25": {
		"NamedInsured":    InsuredNamePlusDBAField,
		"CameraProvider":  CameraProviderMotiveField,
		"NumberOfCameras": NumberOfCamerasField,
		"CameraSubsidy":   CameraSubsidyAmountField,
	},
	"NDCPO B 06 25": {
		"NamedInsured":    InsuredNamePlusDBAField,
		"CameraProvider":  CameraProviderSamsaraField,
		"NumberOfCameras": NumberOfCamerasField,
		"CameraSubsidy":   CameraSubsidyAmountField,
	},
	"CA 23 26 07 25": {
		"PolicyNumber":             PolicyNumberFieldAL,
		"NamedInsured":             InsuredNamePlusDBAField,
		"EndorsementEffectiveDate": PolicyEffectiveDateField,
		"Company":                  InsuranceCarrierOnFormFooterField,
		"IsSingleLimitBIAndPD":     CheckboxCheckedField,
		"SingleLimitBIAndPD":       SingleLimitBIAndPDField,
	},
	"IL 01 46 07 25": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 01 26 01 25": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA N 002 07 25": {
		"NamedInsured":        InsuredNamePlusDBAField,
		"CompanyName":         InsuranceCarrierField,
		"PolicyNumber":        PolicyNumberFieldAL,
		"PolicyEffectiveDate": PolicyEffectiveDateField,
		"Company":             InsuranceCarrierOnFormFooterField,
	},
	"NIS CA U 004 07 25": {
		"PolicyNumber":        PolicyNumberFieldAL,
		"Company":             InsuranceCarrierOnFormFooterField,
		"NamedInsured":        InsuredNamePlusDBAField,
		"PolicyEffectiveDate": PolicyEffectiveDateField,
		"Producer":            InsuranceProducerField,
		"CompanyName":         InsuranceCarrierField,
	},
	"CA 21 16 08 25": {
		"PolicyNumber":             PolicyNumberFieldAL,
		"Company":                  InsuranceCarrierOnFormFooterField,
		"NamedInsured":             InsuredNamePlusDBAField,
		"EndorsementEffectiveDate": PolicyEffectiveDateField,
		"LimitOfInsuranceUMBI":     LimitOfInsuranceUMField,
		"UMUIMCheckbox":            UMUIMCheckboxField,
	},
	"NIS IM 035 WA 05 25": {
		"PolicyNumber":             PolicyNumberFieldMTC,
		"NamedInsured":             InsuredNamePlusDBAField,
		"EndorsementEffectiveDate": PolicyEffectiveDateField,
		"PolicyPeriodTo":           PolicyExpirationDateField,
		"Company":                  InsuranceCarrierOnFormFooterField,
	},
	"NIS IM 036 WA 05 25": {
		"PolicyNumber":                   PolicyNumberFieldMTC,
		"NamedInsured":                   InsuredNamePlusDBAField,
		"PolicyPeriodFrom":               PolicyEffectiveDateField,
		"PolicyPeriodTo":                 PolicyExpirationDateField,
		"NumberOfDaysNoticeCancellation": NumberOfDaysNoticeCancellationField,
		"NumberOfDaysNoticeNonpayment":   NumberOfDaysNoticeNonpaymentField,
		"Company":                        InsuranceCarrierOnFormFooterField,
	},
}

var BusinessAutoFormCodeToFieldConfigs = map[string]map[string]model.Field{
	"CA 00 01 11 20": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 21 53 01 23": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 01 20 01 15": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 04 49 11 16": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 02 70 01 18": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 05 24 11 20": {
		"PolicyNumber":             PolicyNumberFieldAL,
		"EndorsementEffectiveDate": PolicyEffectiveDateField,
		"NamedInsured":             InsuredNameField,
		"Company":                  InsuranceCarrierOnFormFooterField,
	},
	"CA 01 17 04 22": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 04 43 12 23": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 04 44 10 13": {
		"PolicyNumber":               PolicyNumberFieldAL,
		"NamedInsured":               InsuredNameField,
		"EndorsementEffectiveDate":   PolicyEffectiveDateField,
		"NameofPersonOrOrganisation": BlanketAppliesField,
		"Company":                    InsuranceCarrierOnFormFooterField,
	},
	"CA 05 31 12 23": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 20 01 11 20": {
		"Company":                  InsuranceCarrierOnFormFooterField,
		"NamedInsured":             InsuredNameField,
		"EndorsementEffectiveDate": PolicyEffectiveDateField,
		"CompanyName":              InsuranceCarrierField,
		"EffectiveDate":            PolicyEffectiveDateField,
		"ExpirationDate":           PolicyExpirationDateField,
		"PolicyNumber":             PolicyNumberFieldAL,
		"Address":                  PhysicalAddressField,
	},
	"CA 20 05 10 13": {
		"Company":                  InsuranceCarrierOnFormFooterField,
		"PolicyNumber":             PolicyNumberFieldAL,
		"NamedInsured":             InsuredNameField,
		"EndorsementEffectiveDate": PolicyEffectiveDateField,
		// TODO
		//"CoveredALLimit":           CoveredALLimitField,
		//"CoveredALPremium":         CoveredALPremiumField,
	},
	"CA 20 15 11 20": {
		"Company":                  InsuranceCarrierOnFormFooterField,
		"PolicyNumber":             PolicyNumberFieldAL,
		"NamedInsured":             InsuredNameField,
		"EndorsementEffectiveDate": PolicyEffectiveDateField,
		// TODO
	},
	"CA 20 48 10 13": {
		"Company":                    InsuranceCarrierOnFormFooterField,
		"NamedInsured":               InsuredNameField,
		"EndorsementEffectiveDate":   PolicyEffectiveDateField,
		"PolicyNumber":               PolicyNumberFieldAL,
		"NameOfPersonOrOrganisation": BlanketAppliesField,
	},
	"CA 20 54 11 20": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 20 70 10 13": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 21 30 01 15": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 21 38 10 13": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 21 53 10 13": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 23 01 10 13": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 21 03 10 13": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 21 45 10 13": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 99 24 10 13": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"IL 02 83 11 18": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"IL U 024 04 12": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 21 33 08 17": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 31 17 10 13": {
		"Company":                  InsuranceCarrierOnFormFooterField,
		"PolicyNumber":             PolicyNumberFieldAL,
		"NamedInsured":             InsuredNameField,
		"EndorsementEffectiveDate": PolicyEffectiveDateField,
		"LimitOfInsuranceUMPD":     LimitOfInsuranceUMPDField,
	},
	"IL 02 44 09 07": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"IL N 082 09 03": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 01 60 10 13": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"IL 02 76 09 08": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 21 12 10 13": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"IL U 027 03 05": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"IL U 028 03 05": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"IL U 029 03 05": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 01 19 10 13": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 04 33 10 13": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 21 44 12 15": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 31 16 12 15": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"IL 01 17 12 10": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"IL 01 56 11 17": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"IL 01 58 09 08": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"IL 12 11 07 24": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"IL N 040 09 03": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"IL N 120 10 05": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"IL U 070 07 17": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"IL 01 62 10 13": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"IL U 003 01 15": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 99 33 10 13": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"IL 00 17 11 98": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"NIS CA 001 01 24": {
		"PolicyNumber": PolicyNumberFieldAL,
		"Company":      InsuranceCarrierOnFormFooterField,
	},
	"NIS CA 011 01 24": {
		"Company":      InsuranceCarrierOnFormFooterField,
		"PolicyNumber": PolicyNumberFieldAL,
	},
	"NIS IL DS 002 01 24": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"NIS IL DS 003 01 24": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"NIS IL DS 004 01 24": {
		"Company":       InsuranceCarrierOnFormFooterField,
		"PolicyNumber":  PolicyNumberFieldAL,
		"NamedInsured":  InsuredNameField,
		"EffectiveDate": PolicyEffectiveDateField,
		"AgentName":     AgentNameField,
		"AgentNumber":   AgentNumberField,
	},
	"CA 01 38 05 20": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 01 46 10 13": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 01 75 10 13": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 02 05 05 14": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 02 18 11 22": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 21 04 10 13": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 21 20 02 22": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 21 39 05 24": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 21 40 05 24": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 22 25 11 24": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 23 45 11 20": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 23 84 10 13": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 23 85 10 13": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 28 03 12 23": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 99 44 10 13": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"IL 02 50 09 08": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"IL N 001 09 03": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"IL N 098 09 03": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"IL N 014 09 03": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"IL U 030 07 20": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"IL U 050 01 23": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"NIS CA 010 TX 01 24": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"NIS IL 008 IL 01 24": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 01 45 10 13": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"IL 02 72 07 24": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 04 23 10 13": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 04 70 11 24": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 22 26 01 15": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 20 55 10 13": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 99 10 10 13": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 99 16 10 13": {
		"Company":                  InsuranceCarrierOnFormFooterField,
		"PolicyNumber":             PolicyNumberFieldAL,
		"NamedInsured":             InsuredNameField,
		"EndorsementEffectiveDate": PolicyEffectiveDateField,
	},
	"CA 99 23 10 13": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 99 87 10 13": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 99 90 10 13": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"IL 12 01 11 85": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"IL 12 09 08 23": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"MST-OFAC-022024": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"MST-Privacy-1 03 2024": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"MST-TRIA-D1 102023": {
		"AcceptanceOfTerrorismCoverage": AcceptanceOfTerrorismCoverageField,
		"DeclineOfTerrorismCoverage":    DeclineOfTerrorismCoverageField,
		"CompanyName":                   InsuranceCarrierField,
		"PolicyNumberAL":                PolicyNumberFieldAL,
		"Company":                       InsuranceCarrierOnFormFooterField,
	},
	"MST-TSIC-Cover-022025": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"IL 12 04 12 98": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"NIS CA 010 IL 01 24": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"NIS IL 004 01 24": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"NIS CA 002 06 25": {
		"Company":      InsuranceCarrierOnFormFooterField,
		"PolicyNumber": PolicyNumberFieldAL,
	},
	"NIS CA 002 AZ 06 25": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"NIS CA 002 MN 06 25": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"NIS CA 002 MO 06 25": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"NIS CA 002 NE 06 25": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"NIS CA 003 06 25": {
		"PolicyNumber": PolicyNumberFieldAL,
		"Company":      InsuranceCarrierOnFormFooterField,
	},
	"NIS CA 003 IL 06 25": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"NIS CA 004 06 25": {
		"PolicyNumber": PolicyNumberFieldAL,
		"Company":      InsuranceCarrierOnFormFooterField,
	},
	"NIS CA 005 06 25": {
		"PolicyNumber": PolicyNumberFieldAL,
		"Company":      InsuranceCarrierOnFormFooterField,
	},
	"NIS CA 005 IL 06 25": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"NIS CA 006 06 25": {
		"PolicyNumber": PolicyNumberFieldAL,
		"Company":      InsuranceCarrierOnFormFooterField,
	},
	"NIS CA 006 IL 06 25": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"NIS CA 007 06 25": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"NIS CA 018 06 25": {
		"Company":      InsuranceCarrierOnFormFooterField,
		"PolicyNumber": PolicyNumberFieldAL,
	},
	"NIS CA 018 KS 06 25": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 04 30 11 20": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 23 86 10 13": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 23 87 10 13": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 99 17 10 13": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"NIS CA 002 MI 06 25": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"NIS CA 005 KS 06 25": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"NIS IL DS 005 06 25": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"NIS CA DS 002 06 25": {
		"Company":                          InsuranceCarrierOnFormFooterField,
		"PolicyNumber":                     PolicyNumberFieldAL,
		"CompanyName":                      InsuranceCarrierField,
		"CompanyMailingAddress":            InsuranceCarrierMailingAddressField,
		"Producer":                         InsuranceProducerField,
		"ProducerMailingAddress":           InsuranceProducerMailingAddressField,
		"Broker":                           BrokerField,
		"BrokerMailingAddress":             BrokerMailingAddressField,
		"NamedInsured":                     InsuredNameField,
		"InsuredMailingAddress":            PhysicalAddressField,
		"PolicyPeriodFrom":                 PolicyEffectiveDateField,
		"PolicyPeriodTo":                   PolicyExpirationDateField,
		"FormsOfBusinessCorporation":       FormsOfBusinessCorporationField,
		"FormsOfBusinessLLC":               FormsOfBusinessLLCField,
		"PremiumShownIsPayableAtInception": TotalPremium,
		"CounterSignatureOfAuthorizedRepresentativeDate": Date,
	},
	"IL U 047 03 06": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"IL U 062 06 08": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 99 03 10 13": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"NIS CA 010 01 24": {
		"PolicyNumber": PolicyNumberFieldAL,
		"Company":      InsuranceCarrierOnFormFooterField,
	},
	"NIS IL 008 01 24": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"NIS IL N 012 IL 06 25": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"NIS IL N 012 IN 06 25": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"NIS IL N 012 OH 06 25": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"TSIC‐004‐0424": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"NIS CA 001 TX 01 24": {
		"Company":      InsuranceCarrierOnFormFooterField,
		"PolicyNumber": PolicyNumberFieldAL,
	},
	"NIS CA 011 TX 01 24": {
		"Company":      InsuranceCarrierOnFormFooterField,
		"PolicyNumber": PolicyNumberFieldAL,
	},
	"NIS IL 008 TX 01 24": {
		"Company":      InsuranceCarrierOnFormFooterField,
		"PolicyNumber": PolicyNumberFieldAL,
	},
	"CA 01 96 11 20": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 02 43 11 13": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 05 06 11 20": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 28 04 09 22": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
	"CA 04 01 11 20": { // check APD
		"Company":                  InsuranceCarrierOnFormFooterField,
		"PolicyNumber":             PolicyNumberFieldAL,
		"NamedInsured":             InsuredNameField,
		"EndorsementEffectiveDate": PolicyEffectiveDateField,
		//"PDComprehensivePremium":             PDComprehensivePremium,
		//"PDCollisionPremium":                 PDCollisionPremium,
		//"PDComprehensiveAndCollisionPremium": PDComprehensiveAndCollisionPremium,
	},
	"CA 21 09 10 13": { //check UMUIM
		"Company":                  InsuranceCarrierOnFormFooterField,
		"PolicyNumber":             PolicyNumberFieldAL,
		"NamedInsured":             InsuredNameField,
		"EndorsementEffectiveDate": PolicyEffectiveDateField,
		//"LimitOfInsuranceUMBI":     LimitOfInsuranceUMBIField,
	},
	"CA 22 64 10 13": { //TODO: check for PIP
		"Company":                  InsuranceCarrierOnFormFooterField,
		"PolicyNumber":             PolicyNumberFieldAL,
		"NamedInsured":             InsuredNameField,
		"EndorsementEffectiveDate": PolicyEffectiveDateField,
		//"PIPLimitOfInsurance":      PIPLimitOfInsuranceField,
		//"PIPPremium":               PIPPremiumField,
	},
	"CA U 005 05 13": { //TODO: check
		"Company":                InsuranceCarrierOnFormFooterField,
		"PolicyNumber":           PolicyNumberFieldAL,
		"PolicyEffectiveDate":    PolicyEffectiveDateField,
		"CompanyName":            InsuranceCarrierField,
		"Producer":               InsuranceProducerField,
		"ApplicantOrNameInsured": InsuredNameField,
	},
	"IL N 101 10 23": {
		"Company":               InsuranceCarrierOnFormFooterField,
		"CompanyName":           InsuranceCarrierField,
		"CompanyMailingAddress": InsuranceCarrierMailingAddressField,
		"CompanyPhoneNumber":    InsuranceCarrierPhoneNumberField,
		"CompanyWebAddress":     InsuranceCarrierWebsiteField,
	},
	"IL U 071 01 11": { //TODO:check
		"Company":                InsuranceCarrierOnFormFooterField,
		"PolicyNumber":           PolicyNumberFieldAL,
		"PolicyEffectiveDate":    PolicyEffectiveDateField,
		"CompanyName":            InsuranceCarrierField,
		"Producer":               InsuranceProducerField,
		"ApplicantOrNameInsured": InsuredNameField,
	},
	"NIS IL N 012 TX 06 25": {
		"Company": InsuranceCarrierOnFormFooterField,
	},
}
