package business_auto

import (
	"context"
	"time"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/business-auto/model"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/str_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	policyenums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/forms/fill_inputs"
	"nirvanatech.com/nirvana/forms/fill_inputs/models"
	"nirvanatech.com/nirvana/pdffill/requests"
	"nirvanatech.com/nirvana/policy_common/constants"
)

var carrierNameGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*model.BusinessAutoApp, any, any],
) (models.CommonData[*model.BusinessAutoApp, any, any], error) {
	data.FillInputs.ComputedFields.InsuranceCarrier = constants.InsuranceCarrierMSTransverse.String()
	return *data, nil
}

var carrierMailingAddressGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*model.BusinessAutoApp, any, any],
) (models.CommonData[*model.BusinessAutoApp, any, any], error) {
	data.FillInputs.ComputedFields.InsuranceCarrierMailingAddress = data.App
	return *data, nil
}

var insuranceCarrierOnFormFooterGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*model.BusinessAutoApp, any, any],
) (models.CommonData[*model.BusinessAutoApp, any, any], error) {
	_, ok := fill_inputs.StateRequiresCarrierOnForms[data.App.CompanyInfo.USState]
	if !ok {
		data.FillInputs.ComputedFields.InsuranceCarrierOnFormFooter = constants.InsuranceCarrierEmpty.String()
		return *data, nil
	}

	data.FillInputs.ComputedFields.InsuranceCarrierOnFormFooter = constants.InsuranceCarrierMSTransverse.String()
	return *data, nil
}

var insuranceCarrierWebAddressGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*model.BusinessAutoApp, any, any],
) (models.CommonData[*model.BusinessAutoApp, any, any], error) {
	data.FillInputs.ComputedFields.InsuranceCarrierWebsite = constants.InsuranceCarrierMSTransverse.WebAddress()
	return *data, nil
}

var insuranceCarrierPhoneNumberGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*model.BusinessAutoApp, any, any],
) (models.CommonData[*model.BusinessAutoApp, any, any], error) {
	data.FillInputs.ComputedFields.InsuranceCarrierPhoneNumber = constants.InsuranceCarrierMSTransverse.PhoneNumber()
	return *data, nil
}

var dateNowGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*model.BusinessAutoApp, any, any],
) (models.CommonData[*model.BusinessAutoApp, any, any], error) {
	data.FillInputs.ComputedFields.Date = time.Now().Format(time_utils.USLayout)
	return *data, nil
}

var insuredNameGen = func(
	ctx context.Context,
	deps *fill_inputs.Deps,
	data *models.CommonData[*model.BusinessAutoApp, any, any],
) (models.CommonData[*model.BusinessAutoApp, any, any], error) {
	data.FillInputs.ComputedFields.CompanyInfo.Name = data.App.CompanyInfo.Name
	return *data, nil
}

var agentNameGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*model.BusinessAutoApp, any, any],
) (models.CommonData[*model.BusinessAutoApp, any, any], error) {
	data.FillInputs.ComputedFields.AgentName = constants.InsuranceProducerNirvana.String()
	return *data, nil
}

var agentNumberGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*model.BusinessAutoApp, any, any],
) (models.CommonData[*model.BusinessAutoApp, any, any], error) {
	agentNumber, err := constants.InsuranceProducerNirvana.AgentPhoneNumber(policyenums.ProgramTypeBusinessAuto)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to get agent phone number for business auto")
	}
	data.FillInputs.ComputedFields.AgentNumber = agentNumber
	return *data, nil
}

var policyEffectiveDateGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*model.BusinessAutoApp, any, any],
) (models.CommonData[*model.BusinessAutoApp, any, any], error) {
	date := data.App.EffectiveDurationStart.Format(time_utils.USLayout)
	data.FillInputs.ComputedFields.PolicyInfo.PolicyPeriod.From.Date = date
	return *data, nil
}

var policyExpirationDateGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*model.BusinessAutoApp, any, any],
) (models.CommonData[*model.BusinessAutoApp, any, any], error) {
	date := data.App.EffectiveDurationEnd.Format(time_utils.USLayout)
	data.FillInputs.ComputedFields.PolicyInfo.PolicyPeriod.To.Date = date
	return *data, nil
}

var acceptanceOfTerrorismCoverageGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*model.BusinessAutoApp, any, any],
) (models.CommonData[*model.BusinessAutoApp, any, any], error) {
	hasTerrorismCoverage, err := hasTerrorismCoverage(data.IB)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to check for terrorism coverage")
	}
	data.FillInputs.ComputedFields.PolicyInfo.AcceptanceOfTerrorismCoverage = requests.ReturnExportedValue(hasTerrorismCoverage)
	return *data, nil
}

var declineOfTerrorismCoverageGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*model.BusinessAutoApp, any, any],
) (models.CommonData[*model.BusinessAutoApp, any, any], error) {
	hasTerrorismCoverage, err := hasTerrorismCoverage(data.IB)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to check for terrorism coverage")
	}
	data.FillInputs.ComputedFields.PolicyInfo.DeclineOfTerrorismCoverage = requests.ReturnExportedValue(!hasTerrorismCoverage)
	return *data, nil
}

var brokerNameGen = func(
	ctx context.Context,
	deps *fill_inputs.Deps,
	data *models.CommonData[*model.BusinessAutoApp, any, any],
) (models.CommonData[*model.BusinessAutoApp, any, any], error) {
	entityLicense, err := deps.EntityLicence.GetByEntityIDAndUSState(ctx, data.App.AgencyID, data.App.CompanyInfo.USState)
	if err != nil {
		log.Info(
			ctx,
			"failed to get entity license for agency ID %s and US state %s: %v",
			log.Any("agencyID", data.App.AgencyID),
			log.Any("USState", data.App.CompanyInfo.USState),
			log.Err(err),
		)
		return *data, nil
	}
	if entityLicense != nil && entityLicense.EntityName != nil {
		data.FillInputs.ComputedFields.Broker = *entityLicense.EntityName
	}
	return *data, nil
}

var brokerMailingAddressGen = func(
	ctx context.Context,
	deps *fill_inputs.Deps,
	data *models.CommonData[*model.BusinessAutoApp, any, any],
) (models.CommonData[*model.BusinessAutoApp, any, any], error) {
	entityLicense, err := deps.EntityLicence.GetByEntityIDAndUSState(
		ctx,
		data.App.AgencyID,
		data.App.CompanyInfo.USState,
	)
	if err != nil {
		log.Info(
			ctx,
			"failed to get entity license for agency ID %s and US state %s: %v",
			log.Any("agencyID", data.App.AgencyID),
			log.Any("USState", data.App.CompanyInfo.USState),
			log.Err(err),
		)
		return *data, nil
	}

	if entityLicense != nil && entityLicense.BusinessAddress != nil {
		data.FillInputs.ComputedFields.BrokerMailingAddress = *entityLicense.BusinessAddress.AsString()
	}
	return *data, nil
}

var formsOfBusinessLLCGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*model.BusinessAutoApp, any, any],
) (models.CommonData[*model.BusinessAutoApp, any, any], error) {
	if fill_inputs.GetFormOfBusiness(data.App.CompanyInfo.Name) == fill_inputs.FormOfBusinessLLC {
		data.FillInputs.ComputedFields.CompanyInfo.FormOfBusinessLLC = requests.ReturnExportedValue(true)
	}
	return *data, nil
}

var formsOfBusinessCorporationGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*model.BusinessAutoApp, any, any],
) (models.CommonData[*model.BusinessAutoApp, any, any], error) {
	if fill_inputs.GetFormOfBusiness(data.App.CompanyInfo.Name) == fill_inputs.FormOfBusinessCorporation {
		data.FillInputs.ComputedFields.CompanyInfo.FormOfBusinessCorporation = requests.ReturnExportedValue(true)
	}
	return *data, nil
}

var totalPremiumGen = func(
	ctx context.Context,
	deps *fill_inputs.Deps,
	data *models.CommonData[*model.BusinessAutoApp, any, any],
) (models.CommonData[*model.BusinessAutoApp, any, any], error) {
	// charges, err := data.IB.GetChargesWithDistribution(nil, true)
	// if err != nil {
	// 	log.Error(ctx, "failed to get charges with distribution: %v", log.Err(err))
	// 	// return *data, errors.Wrapf(err, "failed to get charges with distribution")
	// }
	// totalCharge, _ := charges.TotalCharge.Float64()

	// data.FillInputs.ComputedFields.PolicyInfo.TotalPremium = str_utils.NumberToLocaleString(totalCharge, 0)

	data.FillInputs.ComputedFields.PolicyInfo.TotalPremium = str_utils.NumberToLocaleString(100, 0)
	return *data, nil
}

var insuredPhysicalAddressGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*model.BusinessAutoApp, any, any],
) (models.CommonData[*model.BusinessAutoApp, any, any], error) {
	primaryInsuredAddress := data.IB.GetLastSegment().GetPrimaryInsured().Address

	// Safely build the physical address string with nil checks
	var addressParts []string

	if primaryInsuredAddress.Street != nil && *primaryInsuredAddress.Street != "" {
		addressParts = append(addressParts, *primaryInsuredAddress.Street)
	}
	if primaryInsuredAddress.City != nil && *primaryInsuredAddress.City != "" {
		addressParts = append(addressParts, *primaryInsuredAddress.City)
	}
	if primaryInsuredAddress.State != nil && *primaryInsuredAddress.State != "" {
		addressParts = append(addressParts, *primaryInsuredAddress.State)
	}
	if primaryInsuredAddress.ZipCode != nil && *primaryInsuredAddress.ZipCode != "" {
		addressParts = append(addressParts, *primaryInsuredAddress.ZipCode)
	}

	physicalAddress := ""
	if len(addressParts) > 0 {
		physicalAddress = addressParts[0]
		for i := 1; i < len(addressParts); i++ {
			physicalAddress += ", " + addressParts[i]
		}
	}

	data.FillInputs.ComputedFields.CompanyInfo.Address.PhysicalAddress = physicalAddress
	return *data, nil
}

var coverageUninsuredMotoristPropertyDamageLimitGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*model.BusinessAutoApp, any, any],
) (models.CommonData[*model.BusinessAutoApp, any, any], error) {
	coverageLimits := data.IB.GetLastSegment().GetCoverageCriteria().GetLimits()
	for _, limit := range coverageLimits {
		for _, subCoverage := range limit.SubCoverageIds {
			if subCoverage == enums.CoverageUninsuredMotoristPropertyDamage.String() {
				data.FillInputs.ComputedFields.CoveragesInfo.CoverageUninsuredMotoristPropertyDamage.Limit = str_utils.NumberToLocaleString(
					limit.Amount,
					0,
				)
				break
			}
		}
	}

	return *data, nil
}
