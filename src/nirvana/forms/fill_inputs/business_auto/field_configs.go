package business_auto

import (
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/forms/fill_inputs/models/business_auto"
)

var policyNumberAL = createPolicyNumberConfig(app_enums.CoverageAutoLiability, true)

var policyEffectiveDate = business_auto.NewFieldConfig(policyEffectiveDateGen, nil, true)

var policyExpirationDate = business_auto.NewFieldConfig(policyExpirationDateGen, nil, true)

var carrierName = business_auto.NewFieldConfig(carrierNameGen, carrierNameValidator, true)

var insuredName = business_auto.NewFieldConfig(insuredNameGen, nil, true)

var insuranceCarrierOnFormFooter = business_auto.NewFieldConfig(insuranceCarrierOnFormFooterGen, nil, false)

var insuranceCarrierWebAddress = business_auto.NewFieldConfig(insuranceCarrierWebAddressGen, nil, false)

var insuranceCarrierPhoneNumber = business_auto.NewFieldConfig(insuranceCarrierPhoneNumberGen, nil, false)

var dateNow = business_auto.NewFieldConfig(dateNowGen, nil, false)

var acceptanceOfTerrorismCoverage = business_auto.NewFieldConfig(acceptanceOfTerrorismCoverageGen, nil, true)

var declineOfTerrorismCoverage = business_auto.NewFieldConfig(declineOfTerrorismCoverageGen, nil, true)

var carrierMailingAddress = business_auto.NewFieldConfig(carrierMailingAddressGen, nil, false)

var brokerName = business_auto.NewFieldConfig(brokerNameGen, nil, false)

var brokerMailingAddress = business_auto.NewFieldConfig(brokerMailingAddressGen, nil, false)

var formsOfBusinessLLC = business_auto.NewFieldConfig(formsOfBusinessLLCGen, nil, false)

var formsOfBusinessCorporation = business_auto.NewFieldConfig(formsOfBusinessCorporationGen, nil, false)

var totalPremium = business_auto.NewFieldConfig(totalPremiumGen, nil, true)

var insuredPhysicalAddress = business_auto.NewFieldConfig(insuredPhysicalAddressGen, nil, false)

var coverageUninsuredMotoristPropertyDamageLimit = business_auto.NewFieldConfig(coverageUninsuredMotoristPropertyDamageLimitGen, nil, false)

var agentName = business_auto.NewFieldConfig(agentNameGen, nil, false)

var agentNumber = business_auto.NewFieldConfig(agentNumberGen, nil, false)
