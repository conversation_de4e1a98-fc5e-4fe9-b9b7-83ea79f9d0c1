package fill_inputs

import (
	"strings"

	"nirvanatech.com/nirvana/common-go/us_states"
)

// StateRequiresCarrierOnForms maps US states to whether they require carrier information on forms
var StateRequiresCarrierOnForms = map[us_states.USState]bool{
	us_states.MI: true,
}

var DaysNoticeForNonPayment = map[us_states.USState]string{
	// 10-Day Notice
	us_states.AL: "10", us_states.AR: "10", us_states.AZ: "10", us_states.CA: "10",
	us_states.CO: "10", us_states.GA: "10", us_states.IL: "10", us_states.IN: "10",
	us_states.IA: "10", us_states.KS: "10", us_states.MI: "10", us_states.MN: "10",
	us_states.MO: "10", us_states.NE: "10", us_states.NM: "10", us_states.NV: "10",
	us_states.OH: "10", us_states.OK: "10", us_states.OR: "10", us_states.SC: "10",
	us_states.TN: "10", us_states.TX: "10", us_states.UT: "10", us_states.WI: "10",
	// 14-Day Notice
	us_states.KY: "14",
	// 15-Day Notice
	us_states.NC: "15", us_states.PA: "15",
}

var DaysNoticeForCancellation = map[us_states.USState]string{
	// 7-Day Notice
	us_states.WI: "7",

	// 10-Day Notice
	us_states.IA: "10", us_states.OK: "10", us_states.OR: "10", us_states.TN: "10",
	us_states.TX: "10",

	// 14-Day Notice
	us_states.KY: "14",

	// 15-Day Notice
	us_states.NC: "15", us_states.PA: "15",

	// 20-Day Notice
	us_states.AL: "20", us_states.IN: "20", us_states.MI: "20",

	// 30-Day Notice
	us_states.AR: "30", us_states.CA: "30", us_states.IL: "30", us_states.KS: "30",
	us_states.NM: "30", us_states.NV: "30", us_states.OH: "30", us_states.SC: "30",
	us_states.UT: "30",

	// 45-Day Notice
	us_states.AZ: "45", us_states.CO: "45", us_states.GA: "45",

	// 60-Day Notice
	us_states.MN: "60", us_states.MO: "60", us_states.NE: "60",
}

type FormOfBusiness int

const (
	FormOfBusinessInvalid FormOfBusiness = iota
	FormOfBusinessLLC
	FormOfBusinessCorporation
)

func GetFormOfBusiness(namedInsured string) FormOfBusiness {
	if strings.Contains(strings.ToUpper(namedInsured), "LLC") {
		return FormOfBusinessLLC
	}
	return FormOfBusinessCorporation
}
