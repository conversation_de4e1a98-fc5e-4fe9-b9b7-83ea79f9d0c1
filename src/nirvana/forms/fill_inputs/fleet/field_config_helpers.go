package fleet

import (
	"context"
	"strings"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/str_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/fmcsa"
	fmcsa_models "nirvanatech.com/nirvana/fmcsa/models"
	"nirvanatech.com/nirvana/forms/fill_inputs"
	"nirvanatech.com/nirvana/forms/fill_inputs/models"
	"nirvanatech.com/nirvana/forms/fill_inputs/models/fleet"
	policy_utils "nirvanatech.com/nirvana/policy/fleet"
)

func createPolicyNumberConfig(coverage enums.Coverage, required bool) models.FieldConfig[*application.Application, *application.SubmissionObject, *application.IndicationOption] {
	return fleet.NewFieldConfig(
		func(
			ctx context.Context,
			_ *fill_inputs.Deps,
			data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
		) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
			shortId := data.App.ShortID
			effectiveDate := data.Sub.CoverageInfo.EffectiveDate
			insuranceCarrier := data.App.ModelPinConfig.Application.InsuranceCarrier

			policyNumber, err := policy_utils.GeneratePolicyNumber(
				coverage,
				effectiveDate,
				string(shortId),
				insuranceCarrier,
			)
			if err != nil {
				return *data, errors.Wrapf(err, "failed to generate policy number for coverage %s", coverage)
			}

			if err := models.SetPolicyNumberForCoverage(data, coverage, policyNumber.String()); err != nil {
				return *data, err
			}
			return *data, nil
		},
		nil,
		required,
	)
}

func getDotDetails(ctx context.Context, fmcsaWrapper fmcsa.DataWrapper, dotNumber int64) (*fmcsa_models.DotDetails, error) {
	dotDetails, err := fmcsaWrapper.GetDetailsByDot(ctx, dotNumber)
	if err != nil {
		return nil, errors.Wrap(err, "unable to get dot details")
	}
	if !dotDetails.IsValidCensus || dotDetails.Census.Name == nil ||
		dotDetails.Census.TotalPowerUnits == nil {
		return nil, errors.New("invalid Census of this dot number")
	}
	return dotDetails, nil
}

// findCoverage returns the coverage with the specified type, or nil if not found
func findCoverage(
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
	coverage enums.Coverage,
) *application.CoverageDetails {
	for _, cov := range data.IndOpt.Coverages {
		if cov.CoverageType == coverage {
			return &cov
		}
	}
	return nil
}

func getCoverageLimit(
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
	coverage enums.Coverage,
) string {
	cov := findCoverage(data, coverage)
	if cov != nil && cov.Limit != nil {
		return str_utils.NumberToLocaleString(*cov.Limit, 0)
	}
	return ""
}

func getCoverageDeductible(
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
	coverage enums.Coverage,
) string {
	cov := findCoverage(data, coverage)
	if cov != nil && cov.Deductible != nil {
		return str_utils.NumberToLocaleString(*cov.Deductible, 0)
	}
	return ""
}

func getCoveragePremiumPerHundredMiles(
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
	coverage enums.Coverage,
) string {
	cov := findCoverage(data, coverage)
	if cov != nil && cov.PremiumPerHundredMiles != nil {
		rate := strings.Join([]string{"$", str_utils.NumberToLocaleString(*cov.PremiumPerHundredMiles, 3)}, "")
		rate = strings.Join([]string{rate, "100 Miles"}, "/")
		return rate
	}
	return ""
}

func getCoveragePremium(
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
	coverage enums.Coverage,
) string {
	cov := findCoverage(data, coverage)
	if cov != nil && cov.Premium != nil {
		return str_utils.NumberToLocaleString(*cov.Premium, 2)
	}
	return ""
}

func isCoveragePresent(
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
	coverage enums.Coverage,
) bool {
	return findCoverage(data, coverage) != nil
}
