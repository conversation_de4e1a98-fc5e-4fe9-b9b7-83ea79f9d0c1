package aggregation

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/external/pibit"
)

func TestGenerateAggregationSummary(t *testing.T) {
	ctx := context.Background()
	coverage := app_enums.CoverageGeneralLiability
	start := time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)
	end := time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)
	effectiveDate := time.Date(2025, 8, 13, 12, 0, 0, 0, time.UTC)

	// Dates for testing staleness logic
	staleReportDate := effectiveDate.Add(-1 * (StalenessDuration + time.Hour)) // older than 90 days
	freshReportDate := effectiveDate.Add(-1 * (StalenessDuration - time.Hour)) // newer than 90 days

	// Base loss objects for reuse in tests.
	loss1 := pibit.ProcessedLoss{
		ID:                   "loss1",
		ClaimID:              stringPtr("claim-abc"),
		Coverage:             &coverage,
		PeriodStartDate:      &start,
		PeriodEndDate:        &end,
		LossPaid:             float64Ptr(1000),
		LossReserved:         float64Ptr(500),
		AlaePaid:             float64Ptr(100),
		AlaeExpenseReserve:   float64Ptr(50),
		SubrogationAmount:    float64Ptr(200),
		SalvageAmount:        float64Ptr(100),
		OtherRecovery:        float64Ptr(50),
		Unmapped:             boolPtr(false),
		ReportGenerationDate: &freshReportDate,
	}
	loss2 := pibit.ProcessedLoss{
		ID:              "loss2",
		ClaimID:         stringPtr("claim-xyz"),
		Coverage:        &coverage,
		PeriodStartDate: &start,
		PeriodEndDate:   &end,
		LossPaid:        float64Ptr(300),
		Unmapped:        boolPtr(false),
	}

	tests := []struct {
		name                   string
		processedLosses        []pibit.ProcessedLoss
		coveragePeriods        []CoveragePeriodsWithGaps
		expectedSummaryPeriods int
		expectedClaimCount     int
		expectedGrossLoss      float64
		expectedTags           []pibit.AggregationPeriodTag
		expectErr              bool
	}{
		{
			name:                   "Basic aggregation with one mapped loss and no tags",
			processedLosses:        []pibit.ProcessedLoss{loss1},
			coveragePeriods:        []CoveragePeriodsWithGaps{{Coverage: coverage, Periods: []PeriodsWithGaps{{AggregationPeriod: pibit.Period{FromDate: start, ToDate: end}}}}},
			expectedSummaryPeriods: 1,
			expectedClaimCount:     1,
			expectedGrossLoss:      1000,
			expectedTags:           []pibit.AggregationPeriodTag{},
		},
		{
			name:                   "Multiple losses with same claim ID count as one claim",
			processedLosses:        []pibit.ProcessedLoss{loss1, {ID: "loss1-part2", ClaimID: stringPtr("claim-abc"), Coverage: &coverage, PeriodStartDate: &start, PeriodEndDate: &end, LossPaid: float64Ptr(200), Unmapped: boolPtr(false), ReportGenerationDate: &freshReportDate}},
			coveragePeriods:        []CoveragePeriodsWithGaps{{Coverage: coverage, Periods: []PeriodsWithGaps{{AggregationPeriod: pibit.Period{FromDate: start, ToDate: end}}}}},
			expectedSummaryPeriods: 1,
			expectedClaimCount:     1,
			expectedGrossLoss:      1200,
			expectedTags:           []pibit.AggregationPeriodTag{},
		},
		{
			name:                   "Multiple distinct claims are counted correctly",
			processedLosses:        []pibit.ProcessedLoss{loss1, loss2},
			coveragePeriods:        []CoveragePeriodsWithGaps{{Coverage: coverage, Periods: []PeriodsWithGaps{{AggregationPeriod: pibit.Period{FromDate: start, ToDate: end}}}}},
			expectedSummaryPeriods: 1,
			expectedClaimCount:     2,
			expectedGrossLoss:      1300,
			expectedTags:           []pibit.AggregationPeriodTag{},
		},
		{
			name:                   "Loss with nil claim ID is not counted but financially included",
			processedLosses:        []pibit.ProcessedLoss{loss1, {ID: "loss-nil-claimid", ClaimID: nil, Coverage: &coverage, PeriodStartDate: &start, PeriodEndDate: &end, LossPaid: float64Ptr(50), Unmapped: boolPtr(false), ReportGenerationDate: &freshReportDate}},
			coveragePeriods:        []CoveragePeriodsWithGaps{{Coverage: coverage, Periods: []PeriodsWithGaps{{AggregationPeriod: pibit.Period{FromDate: start, ToDate: end}}}}},
			expectedSummaryPeriods: 1,
			expectedClaimCount:     1,
			expectedGrossLoss:      1050,
			expectedTags:           []pibit.AggregationPeriodTag{},
		},
		{
			name:                   "Gross loss is correctly rounded to two decimal places",
			processedLosses:        []pibit.ProcessedLoss{{ID: "loss-rounding", ClaimID: stringPtr("claim-rounding"), Coverage: &coverage, PeriodStartDate: &start, PeriodEndDate: &end, Unmapped: boolPtr(false), LossPaid: float64Ptr(123.456), LossReserved: float64Ptr(10.111), AlaePaid: float64Ptr(5.555), ReportGenerationDate: &freshReportDate}},
			coveragePeriods:        []CoveragePeriodsWithGaps{{Coverage: coverage, Periods: []PeriodsWithGaps{{AggregationPeriod: pibit.Period{FromDate: start, ToDate: end}}}}},
			expectedSummaryPeriods: 1,
			expectedClaimCount:     1,
			expectedGrossLoss:      128.01,
			expectedTags:           []pibit.AggregationPeriodTag{},
		},
		{
			name:                   "No processed losses returns empty summary and FileMissing tag",
			processedLosses:        []pibit.ProcessedLoss{},
			coveragePeriods:        []CoveragePeriodsWithGaps{{Coverage: coverage, Periods: []PeriodsWithGaps{{AggregationPeriod: pibit.Period{FromDate: start, ToDate: end}}}}},
			expectedSummaryPeriods: 1,
			expectedClaimCount:     0,
			expectedGrossLoss:      0,
			expectedTags:           []pibit.AggregationPeriodTag{pibit.AggregationPeriodTagFileMissing},
		},
		{
			name:                   "Unmapped loss is skipped and period is tagged as missing",
			processedLosses:        []pibit.ProcessedLoss{{ID: "loss-unmapped", ClaimID: stringPtr("claim-unmapped"), Unmapped: boolPtr(true)}},
			coveragePeriods:        []CoveragePeriodsWithGaps{{Coverage: coverage, Periods: []PeriodsWithGaps{{AggregationPeriod: pibit.Period{FromDate: start, ToDate: end}}}}},
			expectedSummaryPeriods: 1,
			expectedClaimCount:     0,
			expectedGrossLoss:      0,
			expectedTags:           []pibit.AggregationPeriodTag{pibit.AggregationPeriodTagFileMissing},
		},
		{
			name:                   "Stale report date adds FileOutOfDate tag",
			processedLosses:        []pibit.ProcessedLoss{{ID: "loss-stale", ClaimID: stringPtr("claim-stale"), Coverage: &coverage, PeriodStartDate: &start, PeriodEndDate: &end, Unmapped: boolPtr(false), ReportGenerationDate: &staleReportDate}},
			coveragePeriods:        []CoveragePeriodsWithGaps{{Coverage: coverage, Periods: []PeriodsWithGaps{{AggregationPeriod: pibit.Period{FromDate: start, ToDate: end}}}}},
			expectedSummaryPeriods: 1,
			expectedClaimCount:     1,
			expectedGrossLoss:      0,
			expectedTags:           []pibit.AggregationPeriodTag{pibit.AggregationPeriodTagFileOutOfDate},
		},
		{
			name:                   "Missing sub-periods with large threshold adds FileOutOfDate tag",
			processedLosses:        []pibit.ProcessedLoss{loss1},
			coveragePeriods:        []CoveragePeriodsWithGaps{{Coverage: coverage, Periods: []PeriodsWithGaps{{AggregationPeriod: pibit.Period{FromDate: start, ToDate: end}, MissingSubPeriods: []pibit.MissingSubPeriod{{LargerThanThreshold: true}}}}}},
			expectedSummaryPeriods: 1,
			expectedClaimCount:     1,
			expectedGrossLoss:      1000,
			expectedTags:           []pibit.AggregationPeriodTag{pibit.AggregationPeriodTagFileOutOfDate},
		},
		{
			name:                   "Both stale report and missing sub-periods adds FileOutOfDate tag only once",
			processedLosses:        []pibit.ProcessedLoss{{ID: "loss-stale", ClaimID: stringPtr("claim-stale"), Coverage: &coverage, PeriodStartDate: &start, PeriodEndDate: &end, Unmapped: boolPtr(false), ReportGenerationDate: &staleReportDate}},
			coveragePeriods:        []CoveragePeriodsWithGaps{{Coverage: coverage, Periods: []PeriodsWithGaps{{AggregationPeriod: pibit.Period{FromDate: start, ToDate: end}, MissingSubPeriods: []pibit.MissingSubPeriod{{LargerThanThreshold: true}}}}}},
			expectedSummaryPeriods: 1,
			expectedClaimCount:     1,
			expectedGrossLoss:      0,
			expectedTags:           []pibit.AggregationPeriodTag{pibit.AggregationPeriodTagFileOutOfDate},
		},
		{
			name:            "Missing coverage in loss triggers error",
			processedLosses: []pibit.ProcessedLoss{{ID: "loss-err-1", Unmapped: boolPtr(false), PeriodStartDate: &start, PeriodEndDate: &end}},
			coveragePeriods: []CoveragePeriodsWithGaps{{Coverage: coverage, Periods: []PeriodsWithGaps{{AggregationPeriod: pibit.Period{FromDate: start, ToDate: end}}}}},
			expectErr:       true,
		},
		{
			name:            "Nil Unmapped in loss triggers error",
			processedLosses: []pibit.ProcessedLoss{{ID: "loss-err-2", Coverage: &coverage, PeriodStartDate: &start, PeriodEndDate: &end, Unmapped: nil}},
			coveragePeriods: []CoveragePeriodsWithGaps{{Coverage: coverage, Periods: []PeriodsWithGaps{{AggregationPeriod: pibit.Period{FromDate: start, ToDate: end}}}}},
			expectErr:       true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			summary, err := GenerateAggregationSummary(ctx, tt.processedLosses, tt.coveragePeriods, effectiveDate)

			if tt.expectErr {
				assert.Error(t, err)
				assert.Nil(t, summary)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, summary)
				assert.Equal(t, 1, len(summary.PeriodSummary))
				assert.Equal(t, tt.expectedSummaryPeriods, len(summary.PeriodSummary[0].PeriodSummary))

				if len(summary.PeriodSummary[0].PeriodSummary) > 0 {
					period := summary.PeriodSummary[0].PeriodSummary[0]
					assert.Equal(t, tt.expectedClaimCount, period.ClaimCount)
					assert.Equal(t, tt.expectedGrossLoss, period.GrossLoss)
					assert.ElementsMatch(t, tt.expectedTags, period.Tags)
				}
			}
		})
	}
}

// Helper functions to create pointers
func boolPtr(b bool) *bool          { return &b }
func float64Ptr(f float64) *float64 { return &f }
func stringPtr(s string) *string    { return &s }
