package aggregation

import (
	"context"
	"sort"
	"strings"
	"time"

	"github.com/cockroachdb/errors"
	"nirvanatech.com/nirvana/common-go/log"

	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/external/pibit"
)

type CoveragePeriodsWithGaps struct {
	Coverage app_enums.Coverage
	Periods  []PeriodsWithGaps
}

type PeriodsWithGaps struct {
	AggregationPeriod  pibit.Period
	MissingSubPeriods  []pibit.MissingSubPeriod
	NumberOfPowerUnits int32
}

func AddGapsToCoveragePeriods(ctx context.Context, resolution DeduplicationResult, aggregationPeriods []CoverageAggregationPeriods) ([]CoveragePeriodsWithGaps, error) {
	var coveragePeriodsWithGaps []CoveragePeriodsWithGaps

	for _, coveragePeriod := range aggregationPeriods {
		covered, err := extractCoveredPeriods(ctx, resolution, coveragePeriod.Coverage)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to extract covered periods for coverage %s", coveragePeriod.Coverage)
		}

		periodGaps := findGapsInAggregationPeriods(coveragePeriod.Periods, covered)

		sort.Slice(periodGaps, func(i, j int) bool {
			return periodGaps[i].AggregationPeriod.FromDate.Before(periodGaps[j].AggregationPeriod.FromDate)
		})
		coveragePeriodsWithGaps = append(coveragePeriodsWithGaps, CoveragePeriodsWithGaps{
			Coverage: coveragePeriod.Coverage,
			Periods:  periodGaps,
		})
	}

	return coveragePeriodsWithGaps, nil
}

func extractCoveredPeriods(ctx context.Context, resolution DeduplicationResult, coverage app_enums.Coverage) ([]pibit.Period, error) {
	var periods []pibit.Period
	seen := make(map[string]bool)

	for _, lossLine := range resolution.SelectedLosses {
		if !shouldPickUpLoss(lossLine, coverage) {
			log.Info(ctx, "skipping loss for covered periods", log.Stringer("coverage", coverage), log.Any("loss_line", lossLine))
			continue
		}

		key := buildPolicyKey(lossLine.Policy)
		if seen[key] {
			continue
		}
		seen[key] = true

		periods = append(periods, pibit.Period{
			FromDate: *lossLine.Policy.EffDate,
			ToDate:   *lossLine.Policy.ExpDate,
		})
	}

	return periods, nil
}

func shouldPickUpLoss(lossLine LossLineWithPolicyAndClaim, coverage app_enums.Coverage) bool {
	coverageInferred := lossLine.Loss.CoverageInferred
	if coverageInferred == nil || lossLine.Policy.EffDate == nil || lossLine.Policy.ExpDate == nil {
		return false
	}

	mappedCoverage, err := getAppEnumCoverageFromCoverageInferred(*coverageInferred)
	if err != nil {
		return false
	}

	return coverage == *mappedCoverage
}

func findGapsInAggregationPeriods(aggPeriods []pibit.PeriodWithPUCount, covered []pibit.Period) []PeriodsWithGaps {
	var gaps []PeriodsWithGaps
	for _, agg := range aggPeriods {
		missing := computePeriodGaps(agg.Period, covered)
		gaps = append(gaps, PeriodsWithGaps{
			AggregationPeriod:  agg.Period,
			MissingSubPeriods:  missing,
			NumberOfPowerUnits: agg.NumberOfPowerUnits,
		})
	}
	return gaps
}

func computePeriodGaps(entirePeriod pibit.Period, coveredPeriods []pibit.Period) []pibit.MissingSubPeriod {
	if len(coveredPeriods) == 0 {
		return []pibit.MissingSubPeriod{{
			Period:              entirePeriod,
			LargerThanThreshold: isPeriodLargerThanThreshold(entirePeriod),
		}}
	}

	sort.Slice(coveredPeriods, func(i, j int) bool {
		return coveredPeriods[i].FromDate.Before(coveredPeriods[j].FromDate)
	})

	merged := mergeCoveredPeriods(coveredPeriods)

	var gaps []pibit.MissingSubPeriod
	cursor := entirePeriod.FromDate

	for _, covered := range merged {
		if cursor.Before(covered.FromDate) {
			gapEnd := covered.FromDate.AddDate(0, 0, -1)
			if !cursor.After(gapEnd) {
				period := pibit.Period{FromDate: cursor, ToDate: gapEnd}
				if !period.FromDate.After(period.ToDate) {
					gaps = append(gaps, pibit.MissingSubPeriod{
						Period:              period,
						LargerThanThreshold: isPeriodLargerThanThreshold(period),
					})
				}
			}
		}
		cursor = covered.ToDate.AddDate(0, 0, 1)
	}

	if cursor.Before(entirePeriod.ToDate) || cursor.Equal(entirePeriod.ToDate) {
		finalGap := pibit.Period{FromDate: cursor, ToDate: entirePeriod.ToDate}
		if !finalGap.FromDate.After(finalGap.ToDate) {
			gaps = append(gaps, pibit.MissingSubPeriod{
				Period:              finalGap,
				LargerThanThreshold: isPeriodLargerThanThreshold(finalGap),
			})
		}
	}

	return gaps
}

func mergeCoveredPeriods(periods []pibit.Period) []pibit.Period {
	if len(periods) == 0 {
		return nil
	}

	merged := []pibit.Period{periods[0]}

	for _, curr := range periods[1:] {
		last := &merged[len(merged)-1]
		if !curr.FromDate.After(last.ToDate.AddDate(0, 0, 1)) {
			if curr.ToDate.After(last.ToDate) {
				last.ToDate = curr.ToDate
			}
		} else {
			merged = append(merged, curr)
		}
	}

	return merged
}

func isPeriodLargerThanThreshold(period pibit.Period) bool {
	const threshold = 90 * 24 * time.Hour // 90 days
	duration := period.ToDate.Sub(period.FromDate)
	return duration > threshold
}

func buildPolicyKey(p pibit.PolicyData) string {
	return strings.Join([]string{
		toStr(p.PolicyNo),
		toStrTime(p.EffDate),
		toStrTime(p.ExpDate),
	}, "::")
}

func toStr(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

func toStrTime(t *time.Time) string {
	if t == nil {
		return ""
	}
	return t.Format("2006-01-02")
}
