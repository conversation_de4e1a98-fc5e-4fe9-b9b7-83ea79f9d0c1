package form_fetcher

import (
	"context"
	"encoding/base64"
	"sort"
	"strings"

	"nirvanatech.com/nirvana/policy_common/forms_generator/cmd/business_auto"

	"nirvanatech.com/nirvana/policy_common/forms_generator/compilation"

	"nirvanatech.com/nirvana/common-go/gworkspace_utils/gsheets_utils"

	nonfleet_admitted "nirvanatech.com/nirvana/policy_common/forms_generator/cmd/non-fleet-admitted"

	"nirvanatech.com/nirvana/policy_common/forms_generator/cmd/fleet"
	"nirvanatech.com/nirvana/policy_common/forms_generator/cmd/matrix_codegen"

	"nirvanatech.com/nirvana/policy_common/forms_generator/cmd/model"

	"nirvanatech.com/nirvana/policy_common/constants"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/aws_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/policy_common/forms_generator/common"
	"nirvanatech.com/nirvana/policy_common/forms_generator/forms"
)

const (
	startingColumnIndexInMatrix = 1
	startingRowIndexInMatrix    = 3
	NumberOfColsInALMatrix      = 17
	NumberOfColsInGenericMatrix = 16
	formCodeCol                 = 0
	quoteDisplayCol             = 1
	formNameCol                 = 2
	// linkAbsCol is not 3 since it represents the index in the sheet and not in the sanitized list of columns.
	linkAbsCol         = 4
	fillablePdfLinkCol = 5
	statesToInclCol    = 7
	statesToExclCol    = 8
	orderCategoryCol   = 9
	conditionsCol      = 10
	expirationDateCol  = 11
	effectiveDateCol   = 12

	applicableInsuranceCarrier  = 13
	applicableAncillaryCoverage = 14
	applicableCompilationTypes  = 15
)

var (
	sheetNames                 = []string{model.CoreForm, model.SignedForm, model.StateForm, model.ManualForm, model.CoverageForm}
	sheetNamesAdmittedNonFleet = []string{model.CoreForm, model.SignedForm, model.ManualForm, model.CoverageForm}
	sheetNamesBusinessAuto     = []string{model.CoreForm, model.SignedForm, model.ManualForm, model.CoverageForm}
	NoneUSStatesSlice          []us_states.USState
)

func GetAllForms(coverage app_enums.Coverage, appType constants.ApplicationType) ([]model.ScheduledFormsMatrixRow, error) {
	// TODO: use FX
	secretsHelper, err := aws_utils.DeprecatedNewSecretsHelper()
	if err != nil {
		return nil, errors.Wrap(err, "Couldn't create secrets helper")
	}
	secrets, err := secretsHelper.GetJsonSecret(
		context.TODO(),
		"rateml-google-sheets-service-account-creds")

	privateKey, err := base64.StdEncoding.DecodeString(
		secrets["private_key_base64"])
	if err != nil {
		panic(err)
	}
	client, err := gsheets_utils.NewGoogleSheetsClient(
		gsheets_utils.GoogleSheetsClientCredentials{
			Email:        secrets["client_email"],
			PrivateKeyID: secrets["private_key_id"],
			PrivateKey:   privateKey,
		})
	if err != nil {
		return nil, errors.Wrap(err, "Couldn't initiate new G-sheet client")
	}

	var allRows []model.ScheduledFormsMatrixRow

	var spreadsheetSheetNames []string
	switch appType {
	case constants.ApplicationTypeFleet:
		spreadsheetSheetNames = sheetNames
	case constants.ApplicationTypeNonFleetAdmitted:
		spreadsheetSheetNames = sheetNamesAdmittedNonFleet
	case constants.ApplicationTypeBusinessAuto:
		spreadsheetSheetNames = sheetNamesBusinessAuto
	}

	for _, sheetName := range spreadsheetSheetNames {
		sheetId := getSheetId(coverage, appType)
		sheet, err := gsheets_utils.GetSheetFromSpreadsheet(
			client, sheetId, sheetName,
		)
		if err != nil {
			return nil, errors.Wrapf(err, "Couldn't get the sheet with name: %s, id: %s", sheetName, sheetId)
		}
		row := gsheets_utils.GetRowFromSheet(sheet, startingRowIndexInMatrix)

		for r := row; !gsheets_utils.IsRowEmpty(r); r = r.Next() {
			rCopy := r
			alMatrixRow, err := sheetsRowToMatrixRow(coverage, *rCopy, sheetName, appType)
			if err != nil {
				return nil, errors.Wrapf(
					err, "Couldn't serialise sheets MatrixRow to struct for sheet %s row at index %d",
					sheetName, row.IndexInSheet(),
				)
			}

			// Check if the variable name is already taken, if so
			// Use the next 2 digits of the Form Code to construct the variable name
			// This logic assumes that after splitting the form code by " " char.
			// It will return at-least 4 elements, if not we ignore that field
			if doesFormVariableNameAlreadyExist(alMatrixRow.FormVariableName, allRows) {
				splitStr := strings.Split(alMatrixRow.FormCode, " ")
				// replace " ", "", "-", "", "&", "" in all entries in split Str
				for i, str := range splitStr {
					splitStr[i] = strings.NewReplacer(" ", "", "-", "", "&", "").Replace(str)
				}
				if len(splitStr) > 3 {
					alMatrixRow.FormVariableName = strings.Join(splitStr[0:4], "")
					if doesFormVariableNameAlreadyExist(strings.Join(splitStr[0:4], ""), allRows) {
						alMatrixRow.FormVariableName = strings.Join(splitStr[0:5], "")
					}
					allRows = append(allRows, *alMatrixRow)
				} else {
					alMatrixRow.FormVariableName = strings.Join(splitStr[0:], "")
					allRows = append(allRows, *alMatrixRow)
				}
			} else {
				allRows = append(allRows, *alMatrixRow)
			}
		}
	}

	sort.Slice(allRows, func(i, j int) bool {
		return allRows[i].FormVariableName < allRows[j].FormVariableName
	})

	return allRows, nil
}

// TODO: Write validation functions for each field
func sheetsRowToMatrixRow(
	coverage app_enums.Coverage,
	r gsheets_utils.Row,
	sheetName string,
	appType constants.ApplicationType,
) (*model.ScheduledFormsMatrixRow, error) {
	var sanitizedRow []string
	noOfCols := 0
	switch coverage {
	case app_enums.CoverageAutoLiability:
		noOfCols = NumberOfColsInALMatrix
	case app_enums.CoverageGeneralLiability, app_enums.CoverageAutoPhysicalDamage, app_enums.CoverageMotorTruckCargo:
		noOfCols = NumberOfColsInGenericMatrix
	}

	for i := startingColumnIndexInMatrix; i <= noOfCols; i++ {
		sanitizedRow = append(sanitizedRow, gsheets_utils.SanitizeCellValue(r.Cell(i)))
	}

	matrixRowVars, err := getMatrixRowVars(coverage, appType, sanitizedRow)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get MatrixRowVars")
	}

	matrixRow, err := fillScheduleFormsMatrixRow(sanitizedRow, matrixRowVars)
	if err != nil {
		return nil, errors.Wrapf(err, "couldn't populate ScheduledFormsMatrixRow")
	}

	formCode := common.StandardizeSpaces(sanitizedRow[formCodeCol])

	var link string
	switch appType {
	case constants.ApplicationTypeFleet:
		link = gsheets_utils.SanitizeCellValue(r.CellHyperLink(linkAbsCol))
	case constants.ApplicationTypeNonFleetAdmitted, constants.ApplicationTypeBusinessAuto:
		formattedFormCode := strings.ReplaceAll(common.StandardizeSpaces(strings.ToUpper(formCode)), " ", "_")
		link = common.GetUnFormattedFormTemplateBucket(appType, formattedFormCode)
	}
	return &model.ScheduledFormsMatrixRow{
		FormScheduleType:             sheetName,
		FormVariableName:             matrixRowVars.FormVariableName,
		FormCode:                     formCode,
		QuoteDisplay:                 sanitizedRow[quoteDisplayCol],
		FormName:                     sanitizedRow[formNameCol],
		Link:                         link,
		IsDynamic:                    matrixRowVars.IsDynamic,
		FillablePdfLink:              sanitizedRow[fillablePdfLinkCol],
		ALPackages:                   matrixRowVars.ALPackages,
		APDPackages:                  matrixRowVars.APDPackages,
		GLPackages:                   matrixRowVars.GLPackages,
		MTCPackages:                  matrixRowVars.MTCPackages,
		StatesToInclude:              matrixRow.StatesToInclude,
		StatesToExclude:              matrixRow.StatesToExclude,
		OrderCategory:                matrixRow.OrderCategory,
		Conditions:                   matrixRow.Conditions,
		StatewiseEffectiveDates:      matrixRow.StatewiseEffectiveDates,
		EffectiveInsuranceCarrier:    matrixRow.EffectiveInsuranceCarrier,
		Coverages:                    matrixRowVars.Coverages,
		ApplicableAncillaryCoverages: matrixRow.ApplicableAncillaryCoverages,
		ApplicableCompilationTypes:   matrixRow.ApplicableCompilationTypes,
	}, nil
}

func fillScheduleFormsMatrixRow(sanitizedRow []string, vars *model.MatrixRowVars) (*model.ScheduledFormsMatrixRow, error) {
	var (
		statesToInclude, statesToExclude                         []us_states.USState
		orderCategory                                            forms.OrderCategory
		conditions                                               string
		statesToIncludeErr, statesToExcludeErr, orderCategoryErr error
	)
	offset := vars.ColOffset

	statesToInclude, statesToIncludeErr = getUSStatesArrayFromString(
		sanitizedRow[statesToInclCol+offset], vars.SupportedStates,
	)
	if statesToIncludeErr != nil {
		return nil, errors.Wrap(statesToIncludeErr, "Couldn't Parse states to include")
	}

	statesToExclude, statesToExcludeErr = getUSStatesArrayFromString(
		sanitizedRow[statesToExclCol+offset], vars.SupportedStates,
	)
	if statesToExcludeErr != nil {
		return nil, errors.Wrap(statesToExcludeErr, "Couldn't Parse states to exclude")
	}

	orderCategory, orderCategoryErr = forms.OrderCategoryString(sanitizedRow[orderCategoryCol+offset])
	if orderCategoryErr != nil {
		return nil, errors.Wrapf(
			orderCategoryErr, "OrderCategory Enum Value not found for %v", sanitizedRow[orderCategoryCol+offset],
		)
	}

	conditions = sanitizedRow[conditionsCol+offset]

	endDates, startDates := sanitizedRow[expirationDateCol+offset], sanitizedRow[effectiveDateCol+offset]
	statewiseEffectiveDates, err := matrix_codegen.ParseStatewiseEffectiveDates(endDates, startDates)
	if err != nil {
		return nil, errors.Wrapf(
			err, "Couldn't Parse statewise effective dates for %v", sanitizedRow[0],
		)
	}

	insuranceCarrier := sanitizedRow[applicableInsuranceCarrier+offset]
	effectiveInsuranceCarrier, err := matrix_codegen.ParseEffectiveInsuranceCarrier(insuranceCarrier)
	if err != nil {
		return nil, errors.Wrapf(
			err, "Couldn't Parse statewise effective insurance carrier for %v", sanitizedRow[0],
		)
	}

	ancillaryCoverages := sanitizedRow[applicableAncillaryCoverage+offset]
	var applicableAncillaryCovs []app_enums.Coverage
	if len(ancillaryCoverages) != 0 {
		ancillaryCoverages = strings.ReplaceAll(ancillaryCoverages, " ", "")
		coverages := strings.Split(ancillaryCoverages, ",")
		for _, covShortCode := range coverages {
			cov, err := app_enums.GetCoverageFromShortCode(covShortCode)
			if err != nil {
				continue
				//return nil, errors.Wrapf(err, "Couldn't parse ancillary coverage %s", covShortCode)
			}

			applicableAncillaryCovs = append(applicableAncillaryCovs, cov)
		}
	}

	compTypes := sanitizedRow[applicableCompilationTypes+offset]
	var applicableCompTypes []compilation.CompilationType
	if len(compTypes) != 0 {
		compilationTypes := strings.Split(compTypes, ",")

		for _, compTypeStr := range compilationTypes {
			compType, err := compilation.CompilationTypeString(compTypeStr)
			if err != nil {
				return nil, errors.Wrapf(err, "Couldn't parse compilation type string %s", compTypeStr)
			}

			applicableCompTypes = append(applicableCompTypes, compType)
		}

	}

	return &model.ScheduledFormsMatrixRow{
		StatesToInclude:              statesToInclude,
		StatesToExclude:              statesToExclude,
		OrderCategory:                orderCategory,
		Conditions:                   conditions,
		StatewiseEffectiveDates:      statewiseEffectiveDates,
		EffectiveInsuranceCarrier:    effectiveInsuranceCarrier,
		ApplicableAncillaryCoverages: applicableAncillaryCovs,
		ApplicableCompilationTypes:   applicableCompTypes,
	}, nil
}

func getMatrixRowVars(
	coverage app_enums.Coverage, appType constants.ApplicationType, row []string,
) (vars *model.MatrixRowVars, err error) {
	switch appType {
	case constants.ApplicationTypeFleet:
		if vars, err = fleet.GetMatrixRowVars(coverage, row); err != nil {
			return nil, err
		}
	case constants.ApplicationTypeNonFleetAdmitted:
		if vars, err = nonfleet_admitted.GetMatrixRowVars(coverage, row); err != nil {
			return nil, err
		}
	case constants.ApplicationTypeBusinessAuto:
		if vars, err = business_auto.GetMatrixRowVars(coverage, row); err != nil {
			return nil, err
		}
	}
	return
}
