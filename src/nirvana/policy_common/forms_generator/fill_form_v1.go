package forms_generator

import (
	"context"
	"strings"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"nirvanatech.com/nirvana/common-go/log"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/policy_common/constants"
	"nirvanatech.com/nirvana/policy_common/forms_generator/common"
	"nirvanatech.com/nirvana/policy_common/forms_generator/compilation"
	"nirvanatech.com/nirvana/policy_common/forms_generator/fill"
	"nirvanatech.com/nirvana/policy_common/forms_generator/fill/fleet"
	nf_admitted "nirvanatech.com/nirvana/policy_common/forms_generator/fill/non_fleet_admitted"
	"nirvanatech.com/nirvana/policy_common/forms_generator/forms"
)

var (
	errNoFormFieldsAffected = errors.New("pdfcpu: no form fields affected")
	errNoFormAvailable      = errors.New("pdfcpu: no form available")
	errNoFormFieldsFound    = errors.New("pdfcpu: no form fields available")
)

func fillFormV1(
	ctx context.Context,
	deps fill.FillDeps,
	accountId uuid.UUID,
	inputs FillInputs,
	formObj forms.Form,
	schType compilation.ScheduleType,
	appType constants.ApplicationType,
	cov app_enums.Coverage,
) (*forms.Form, error) {
	timeNow := time.Now()
	log.Info(ctx, "starting filling form", log.String("form_code", formObj.Code), log.String("fill_method", "fillFormV1"))
	var retval *forms.Form
	// This is being done to also allow filling of forms if
	// the schedule type of form was changed, and we are trying to
	// fill the form with the new schedule type.
	incomingFormSet := forms.NewFormSet()
	incomingFormSet.Add(formObj)
	// check if generator function exist for the following schedule type
	if formObj.Dynamic {
		formObj.Code = common.StandardizeSpaces(formObj.Code)
		fillFn, cov, scheduleType, err := getFillFnWrapper(formObj, cov, schType, appType)
		if err != nil {
			//return nil, errors.Wrapf(
			//	err,
			//	"failed to get fill function for form code %s schedule type %s application type %s",
			//	formObj.Code, schType, appType,
			//)
			return &forms.Form{}, nil
		}
		scheduleTypeInputs, ok := inputs[scheduleType]
		if !ok {
			return nil, errors.Newf("missing fill input  for %s", scheduleType.String())
		}
		dynamicFormSet, err := fillFn(ctx, deps, accountId, scheduleTypeInputs, incomingFormSet, cov, appType)
		if err != nil {
			// check if the error is of type no field filled. If it is then we need to
			// download the static form and upload it. PDFCPU doesn't expose sentinel errors and since
			// this is a wrapped error we need to check the error string instead.
			// TODO replace this with errors.Is once the author makes the sentinel error public
			if strings.Contains(err.Error(), errNoFormFieldsAffected.Error()) ||
				strings.Contains(err.Error(), errNoFormAvailable.Error()) ||
				strings.Contains(err.Error(), errNoFormFieldsFound.Error()) {
				log.Warn(ctx, "failed to fill form", log.Err(err))
				retval, err = common.UploadStaticForm(ctx, deps, accountId, formObj, appType)
				if err != nil {
					return nil, errors.Wrapf(err, "unable to download static form %s", formObj.Name)
				}
				return retval, nil
			}
			return nil, errors.Wrapf(err, "unable to apply fillFn for %s", scheduleType.String())
		}
		if len(dynamicFormSet.ToSlice()) < 1 {
			return nil, errors.Newf("no forms returned for %s schedule type and %s and %s",
				scheduleType.String(), formObj.Code, formObj.ArchivedAt)
		}
		retval = &dynamicFormSet.ToSlice()[0]
	} else {
		var err error
		retval, err = common.UploadStaticForm(ctx, deps, accountId, formObj, appType)
		if err != nil {
			return nil, errors.Wrapf(err, "unable to download static form %s", formObj.Name)
		}
	}
	log.Info(ctx, "completed filling form", log.String("form_code", formObj.Code),
		log.Duration("duration", time.Since(timeNow)),
	)
	return retval, nil
}

func getFillFnWrapper(
	form forms.Form,
	covTyp app_enums.Coverage,
	schType compilation.ScheduleType,
	appType constants.ApplicationType,
) (fill.FillFormsGeneratorFn, app_enums.Coverage, compilation.ScheduleType, error) {
	switch appType {
	case constants.ApplicationTypeFleet:
		return getFillFn(
			form,
			covTyp,
			schType,
			fleet.CheckGeneratorFnExists,
			fleet.FillScheduleTypeFormsGeneratorFns,
		)
	case constants.ApplicationTypeNonFleetAdmitted:
		return getFillFn(
			form,
			covTyp,
			schType,
			nf_admitted.CheckGeneratorFnExists,
			nf_admitted.FillScheduleTypeFormsGeneratorFns,
		)
	default:
		return nil, covTyp, compilation.ScheduleTypeInvalid, errors.Newf("invalid application type %s", appType)
	}
}

func getFillFn(
	form forms.Form,
	covTyp app_enums.Coverage,
	schType compilation.ScheduleType,
	checkGeneratorFn func(formCode string, covTyp app_enums.Coverage, scheduleType *compilation.ScheduleType) (bool, app_enums.Coverage, compilation.ScheduleType),
	generatorFnsMap map[compilation.ScheduleType]fill.FillFormsGeneratorFn,
) (fill.FillFormsGeneratorFn, app_enums.Coverage, compilation.ScheduleType, error) {
	ok, cov, scheduleType := checkGeneratorFn(form.Code, covTyp, &schType)
	if !ok {
		return nil, cov, compilation.ScheduleTypeInvalid, errors.Newf(
			"generator function not found for form %s in %s and %s", form.Code, cov, schType,
		)
	}
	fillFn, ok := generatorFnsMap[scheduleType]
	if !ok {
		return nil, cov, compilation.ScheduleTypeInvalid, errors.Newf(
			"missing fill generator func for %s and %s", cov, scheduleType,
		)
	}
	return fillFn, cov, scheduleType, nil
}
