package forms_generator

import (
	"encoding/json"
	"time"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/us_states"
	app "nirvanatech.com/nirvana/db-api/db_wrappers/application"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/forms/enums"
	"nirvanatech.com/nirvana/policy_common/constants"
	"nirvanatech.com/nirvana/policy_common/forms_generator/compilation"
	"nirvanatech.com/nirvana/telematics"
)

// InitInputs holds all the possible inputs required to initialize a form
// compilation.
type InitInputs struct {
	State                            us_states.USState
	PackageType                      app_enums.IndicationOptionTag
	Coverages                        []app_enums.Coverage
	Metadata                         Metadata
	IsMTCRatingModelV2               bool
	IsExpiringPolicyMTCRatingModelV1 bool
	AgencyId                         string // TODO Remove this field once we have a better way to handle agency based form attachments
}

type Metadata struct {
	PolicyEffectiveDate                     time.Time
	USState                                 us_states.USState
	ALDeductible                            int32
	GLDeductible                            int32
	CoveragesWithCombinedDeductibles        *app.CombinedDeductibleCoverages
	IsAPDMTCDeductibleCombined              bool
	IsNegotiatedPremiumGTTraditionalPremium bool
	IsMinimumMileageGuaranteed              bool
	AttachCGP023                            bool
	AttachCMP004                            bool
	AttachCAP014                            bool
	AttachCGP024                            bool
	AttachCMP008                            bool
	AttachIL0952                            bool
	AttachCA2264                            bool
	AttachCIM7062                           bool
	AttachCIM7058                           bool
	AttachILN08809                          bool
	AttachCG0442                            bool
	AttachCG0441                            bool
	AttachCAP040100NF1123                   bool
	AttachCameraAgreement                   bool
	TSPProvider                             *telematics.TSP
	InsuranceCarrier                        constants.InsuranceCarrier
	AllCoverages                            []app_enums.Coverage
	NumberOfCameras                         int
	SubsidyAmount                           float64
	NumberOfPowerUnits                      int32
	ExpiringPolicyForms                     map[string]map[string]bool // Map of CoverageString vs Flattened FormCode
	IsRenewal                               bool
	ExistingCarrierForRenewalApp            *constants.InsuranceCarrier
}

// FillInputs holds all the possible inputs required to fill out forms for
// a compilation. Unfortunately, we have to hold an interface as the inputs
// will change per ScheduleType.
type FillInputs map[compilation.ScheduleType]interface{}

// FillInputsNew holds all the possible inputs required to fill out forms for
// a compilation.
// This is the new domain entity that will be used to fill out forms.
type FillInputsNew struct {
	IB             interface{}            `json:"IB"`
	ComputedFields ComputedFields         `json:"ComputedFields"`
	Constants      map[string]interface{} `json:"Constants"`
}

// ComputedFields holds the computed fields for a form compilation.
type ComputedFields struct {
	CompanyInfo                            CompanyInfo   `json:"CompanyInfo"`
	PolicyInfo                             PolicyInfo    `json:"PolicyInfo"`
	CoveragesInfo                          CoveragesInfo `json:"CoveragesInfo"`
	InsuranceCarrierOnFormFooter           string        `json:"InsuranceCarrierOnFormFooter"`
	InsuranceCarrier                       string        `json:"InsuranceCarrier"`
	InsuranceCarrierMailingAddress         string        `json:"InsuranceCarrierMailingAddress"`
	InsuranceCarrierPhoneNumber            string        `json:"InsuranceCarrierPhoneNumber"`
	InsuranceCarrierWebsite                string        `json:"InsuranceCarrierWebsite"`
	Date                                   string        `json:"Date"`
	ReferToTerminalSchedule                string        `json:"ReferToTerminalSchedule"`
	AgentName                              string        `json:"AgentName"`
	AgentNumber                            string        `json:"AgentNumber"`
	CoverageReeferWithoutHumanErrorPresent string        `json:"CoverageReeferWithoutHumanErrorPresent"`
	CoverageReeferWithHumanErrorPresent    string        `json:"CoverageReeferWithHumanErrorPresent"`
	ReeferLimitPerVehicle                  string        `json:"ReeferLimitPerVehicle"`
	ReeferBreakdownDeductible              string        `json:"ReeferBreakdownDeductible"`
	UMUIMCheckbox                          string        `json:"UMUIMCheckbox"`
	LimitOfInsuranceUM                     string        `json:"LimitOfInsuranceUM"`
	Broker                                 string        `json:"Broker"`
	BrokerMailingAddress                   string        `json:"BrokerMailingAddress"`
}

type PolicyInfo struct {
	PolicyNumbers                  PolicyNumbers        `json:"PolicyNumbers"`
	PolicyPeriod                   PolicyPeriod         `json:"PolicyPeriod"`
	NumberOfDaysNoticeNonpayment   string               `json:"NumberOfDaysNoticeNonpayment"`
	NumberOfDaysNoticeCancellation string               `json:"NumberOfDaysNoticeCancellation"`
	AcceptanceOfTerrorismCoverage  string               `json:"AcceptanceOfTerrorismCoverage"`
	DeclineOfTerrorismCoverage     string               `json:"DeclineOfTerrorismCoverage"`
	CameraSubsidyDetails           CameraSubsidyDetails `json:"CameraSubsidyDetails"`
	TotalPremium                   string               `json:"TotalPremium"`
}

type CameraSubsidyDetails struct {
	NumberOfCameras     string `json:"NumberOfCameras"`
	CameraSubsidyAmount string `json:"CameraSubsidyAmount"`
}
type PolicyNumbers struct {
	CoverageAutoLiability    string `json:"CoverageAutoLiability"`
	CoverageGeneralLiability string `json:"CoverageGeneralLiability"`
	CoverageMotorTruckCargo  string `json:"CoverageMotorTruckCargo"`
}

// PolicyPeriod holds the policy period information for ComputedFields.
// It contains the start and end dates of the policy period.
// They are defined as structs because we might need to provide dates in different formats in the future,
// for example, DD, MM and YYYY would be required separately in some cases,
// and this allows us to easily extend the structure without breaking existing code.
// The `Date` field will provide date in US Layout
type PolicyPeriod struct {
	From PolicyPeriodFrom `json:"From"`
	To   PolicyPeriodTo   `json:"To"`
}

type PolicyPeriodFrom struct {
	Date string `json:"Date"`
}

type PolicyPeriodTo struct {
	Date string `json:"Date"`
}

// CompanyInfo holds the company information for ComputedFields.
type CompanyInfo struct {
	Address                   Address `json:"Address"`
	Name                      string  `json:"Name"`
	CompanyNamePlusDBA        string  `json:"CompanyNamePlusDBA"`
	FormOfBusinessLLC         string  `json:"FormsOfBusinessLLC"`
	FormOfBusinessCorporation string  `json:"FormOfBusinessCorporation"`
}

// Address holds the address information for CompanyInfo.
type Address struct {
	Street string `json:"Street"`
	City   string `json:"City"`
	State  string `json:"State"`
	Zip    string `json:"Zip"`
	// PhysicalAddress is concatenation of Street, City, State and Zip
	PhysicalAddress string `json:"PhysicalAddress"`
}

type CoveragesInfo struct {
	CoverageMotorTruckCargo                 CoverageInfo `json:"CoverageMotorTruckCargo"`
	CoverageCargoTrailerInterchange         CoverageInfo `json:"CoverageCargoTrailerInterchange"`
	CoverageDebrisRemoval                   CoverageInfo `json:"CoverageDebrisRemoval"`
	CoverageEarnedFreight                   CoverageInfo `json:"CoverageEarnedFreight"`
	CoveragePollutantCleanupAndRemoval      CoverageInfo `json:"CoveragePollutantCleanupAndRemoval"`
	CoverageLossMitigationExpenses          CoverageInfo `json:"CoverageLossMitigationExpenses"`
	CoverageMiscellaneousEquipment          CoverageInfo `json:"CoverageMiscellaneousEquipment"`
	CoverageUninsuredMotoristPropertyDamage CoverageInfo `json:"CoverageUninsuredMotoristPropertyDamage"`
}

type CoverageInfo struct {
	Limit                  string `json:"Limit"`
	Deductible             string `json:"Deductible"`
	PremiumPerHundredMiles string `json:"PremiumPerHundredMiles"`
	Premium                string `json:"Premium"`
	SurplusLinesTax        string `json:"SurplusLinesTax"`
	StampingFee            string `json:"StampingFee"`
}
type FillInputsNewJSON []byte

type FormFillRequest struct {
	FormFields       map[string]any
	FormTemplateType enums.FormTemplateType
	FileName         string
}

func (f *FormFillRequest) MarshalJSON() ([]byte, error) {
	data, err := json.Marshal(f.FormFields)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to marshal extracted data: %v", f.FormFields)
	}
	return data, nil
}

func (f *FormFillRequest) InputPath() string {
	return f.FileName
}
