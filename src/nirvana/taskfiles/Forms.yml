# Taskfile for Forms related commands
version: "3"

vars:
  REPO_ROOT:
    sh: git rev-parse --show-toplevel

tasks:
  backfill-fs-db-data:
    desc: backfill forms data from google sheet matrices
    run: once
    internal: true
    cmds:
      - bazel run //nirvana/cmd/forms -- backfillFormsV2Db
  backfill-fs-manual-rules:
    desc: populate manual rules in forms schedule using hardcoded data
    run: once
    internal: true
    cmds:
      - bazel run //nirvana/cmd/forms -- manualFormsV2Db
  backfill-fs-hardcoded-forms:
    desc: backfill forms data from hardcoded data
    run: once
    internal: true
    cmds:
      - bazel run //nirvana/cmd/forms -- hardcodedFormsV2Db
  backfill-fs:
    desc: backfill forms data to forms schedule db
    run: once
    cmds:
      - task: backfill-fs-db-data
      - task: backfill-fs-manual-rules
      - task: backfill-fs-hardcoded-forms
  codegen:
    desc: generate code for forms
    run: once
    cmds:
      - bazel run //nirvana/policy_common/forms_generator/cmd/forms_generator -- -repo_path {{.REPO_ROOT}} -app_type {{.CLI_ARGS}}