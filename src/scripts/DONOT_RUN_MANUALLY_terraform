#!/bin/bash
# This is a wrapper on top of terraform which fetches current image tags
# and passes them to terraform. These can be overriden individually
# via arguments to terraform.
set -e

TF_BINARY="${TF_BINARY:-terraform}"

echo "Using $TF_BINARY as terraform binary"

echo "Fetching current image tags via terraform output"
TF_OUTPUT_JSON=$($TF_BINARY output -json)

TF_VAR_pdfgen_server_tag=$(echo "$TF_OUTPUT_JSON" | jq -r '.pdfgen_server_tag.value')
export TF_VAR_pdfgen_server_tag

TF_VAR_mvr_cache_server_tag=$(echo "$TF_OUTPUT_JSON" | jq -r '.mvr_cache_server_tag.value')
export TF_VAR_mvr_cache_server_tag

TF_VAR_api_server_tag=$(echo "$TF_OUTPUT_JSON" | jq -r '.api_server_tag.value')
export TF_VAR_api_server_tag

TF_VAR_graphql_server_tag=$(echo "$TF_OUTPUT_JSON" | jq -r '.graphql_server_tag.value')
export TF_VAR_graphql_server_tag

TF_VAR_telematics_server_tag=$(echo "$TF_OUTPUT_JSON" | jq -r '.telematics_server_tag.value')
export TF_VAR_telematics_server_tag

TF_VAR_telematics_grpc_server_tag=$(echo "$TF_OUTPUT_JSON" | jq -r '.telematics_grpc_server_tag.value')
export TF_VAR_telematics_grpc_server_tag

TF_VAR_vehicles_grpc_server_tag=$(echo "$TF_OUTPUT_JSON" | jq -r '.vehicles_grpc_server_tag.value')
export TF_VAR_vehicles_grpc_server_tag

TF_VAR_quote_scraper_grpc_server_tag=$(echo "$TF_OUTPUT_JSON" | jq -r '.quote_scraper_grpc_server_tag.value')
export TF_VAR_quote_scraper_grpc_server_tag

TF_VAR_job_processor_tag=$(echo "$TF_OUTPUT_JSON" | jq -r '.job_processor_tag.value')
export TF_VAR_job_processor_tag

TF_VAR_data_infra_job_processor_tag=$(echo "$TF_OUTPUT_JSON" | jq -r '.data_infra_job_processor_tag.value')
export TF_VAR_data_infra_job_processor_tag

TF_VAR_oauth_server_tag=$(echo "$TF_OUTPUT_JSON" | jq -r '.oauth_server_tag.value')
export TF_VAR_oauth_server_tag

TF_VAR_feature_store_server_tag=$(echo "$TF_OUTPUT_JSON" | jq -r '.feature_store_server_tag.value')
export TF_VAR_feature_store_server_tag

TF_VAR_quoting_job_processor_tag=$(echo "$TF_OUTPUT_JSON" | jq -r '.quoting_job_processor_tag.value')
export TF_VAR_quoting_job_processor_tag

TF_VAR_distsem_server_tag=$(echo "$TF_OUTPUT_JSON" | jq -r '.distsem_server_tag.value')
export TF_VAR_distsem_server_tag

TF_VAR_fmcsa_scraper_tag=$(echo "$TF_OUTPUT_JSON" | jq -r '.fmcsa_scraper_tag.value')
export TF_VAR_fmcsa_scraper_tag

TF_VAR_jobber_monitor_tag=$(echo "$TF_OUTPUT_JSON" | jq -r '.jobber_monitor_tag.value')
export TF_VAR_jobber_monitor_tag

TF_VAR_saferwatch_scraper_tag=$(echo "$TF_OUTPUT_JSON" | jq -r '.saferwatch_scraper_tag.value')
export TF_VAR_saferwatch_scraper_tag

TF_VAR_safety_job_processor_tag=$(echo "$TF_OUTPUT_JSON" | jq -r '.safety_job_processor_tag.value')
export TF_VAR_safety_job_processor_tag

TF_VAR_db_migrate_lambda_tag=$(echo "$TF_OUTPUT_JSON" | jq -r '.db_migrate_lambda_tag.value')
export TF_VAR_db_migrate_lambda_tag

TF_VAR_grpc_lambda_tag=$(echo "$TF_OUTPUT_JSON" | jq -r '.grpc_lambda_tag.value')
export TF_VAR_grpc_lambda_tag

TF_VAR_event_job_processor_tag=$(echo "$TF_OUTPUT_JSON" | jq -r '.event_job_processor_tag.value')
export TF_VAR_event_job_processor_tag

TF_VAR_fmcsa_data_provider_grpc_server_tag=$(echo "$TF_OUTPUT_JSON" | jq -r '.fmcsa_data_provider_grpc_server_tag.value')
export TF_VAR_fmcsa_data_provider_grpc_server_tag

TF_VAR_llmops_server_tag=$(echo "$TF_OUTPUT_JSON" | jq -r '.llmops_server_tag.value')
export TF_VAR_llmops_server_tag

TF_VAR_draft_fnol_lambda_tag=$(echo "$TF_OUTPUT_JSON" | jq -r '.draft_fnol_lambda_tag.value')
export TF_VAR_draft_fnol_lambda_tag

TF_VAR_mcp_experiments_rest_server_tag=$(echo "$TF_OUTPUT_JSON" | jq -r '.mcp_experiments_rest_server_tag.value')
export TF_VAR_mcp_experiments_rest_server_tag

TF_VAR_simulation_job_processor_tag=$(echo "$TF_OUTPUT_JSON" | jq -r '.simulation_job_processor_tag.value')
export TF_VAR_simulation_job_processor_tag

echo "Running terraform"
$TF_BINARY "$@"
