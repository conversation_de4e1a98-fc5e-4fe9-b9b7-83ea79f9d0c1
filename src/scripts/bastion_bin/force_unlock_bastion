#!/bin/bash

# This script is used to force unlock the bastion.

# Exit on error, and pipe failures
set -eo pipefail

lock_info_file="$HOME/nirvana_flock.info"

# Check if lock exists
if [[ ! -f "$lock_info_file" ]]; then
    echo "No lock found. Bastion is already unlocked."
    exit 0
fi

# parse the pid from lock info file and verify that pid is still running to confirm that lock is still valid
first_line=$(head -n 1 "$lock_info_file")

# Parse USER:PID from the first line using regex parsing
if [[ "$first_line" =~ ^([^:]+):([0-9]+)$ ]]; then
    lock_user="${BASH_REMATCH[1]}"
    lock_pid="${BASH_REMATCH[2]}"
else
    echo "Internal error: Invalid lock file format"
    exit 1
fi

# Check if the PID is still running
if [[ -e "/proc/$lock_pid" ]]; then
    cat << EOF
Current lock details:
====================
$(tail -n +2 "$lock_info_file")

Lock is currently held by process $lock_pid (user: $lock_user)

WARNING: Force unlocking will terminate process $lock_pid that is currently holding the lock.
This may abruptly interrupt the lock holder's work and could cause data loss or corruption.

EOF
else
    echo "Bastion is already unlocked."
    exit 0
fi

echo -n "To proceed with force unlocking, please type verbatim 'I know what I am doing': "
read -r confirmation

if [[ "$confirmation" != "I know what I am doing" ]]; then
    echo "Force unlock cancelled."
    exit 1
fi

# Kill the process if it's still running
if [[ -e "/proc/$lock_pid" ]]; then
    echo "Terminating process $lock_pid..."
    if kill -s KILL "$lock_pid" 2>/dev/null; then
        echo "Process $lock_pid terminated successfully."
        rm "$lock_info_file" "$HOME/nirvana_flock" # not necessary, but good to have
    else
        echo "Warning: Failed to terminate process $lock_pid (it may have already exited)"
        exit 1
    fi
fi

echo "Bastion force unlocked successfully."