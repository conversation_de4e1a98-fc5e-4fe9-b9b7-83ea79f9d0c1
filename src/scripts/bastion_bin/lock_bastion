#!/bin/bash

# Exit on error, and pipe failures
set -eo pipefail

# fail if NIRVANA_EMAIL is not set
if [[ -z "$NIRVANA_EMAIL" ]]; then
    echo "Error: NIRVANA_EMAIL environment variable is not set"
    exit 1
fi

# Get current timestamp and reason
timestamp=$(date '+%Y-%m-%d %H:%M:%S')
if [[ -z "$1" ]]; then
    echo -n "Please provide a reason for locking the bastion: "
    read -r reason
    if [[ -z "$reason" ]]; then
        reason="no reason provided"
    fi
else
    reason="$1"
fi

(
    # here -n denotes non-blocking mode,
    # so flock will return immediately with non-zero exit code if the lock is already held
    if ! flock -n 200; then
        cat <<EOF
Error: Lock is already held.
$( [[ -f "$HOME/nirvana_flock.info" ]] && tail -n +2 "$HOME/nirvana_flock.info" )

To forcefully unlock the bastion, run force_unlock_bastion
EOF
        exit 1
    fi

    # Write detailed information to a separate info file
    # First line is machine-friendly format: USER:PID
    # Rest of the lines are human-readable format
    cat > "$HOME/nirvana_flock.info" << EOF
$NIRVANA_EMAIL:$$
    User = $NIRVANA_EMAIL
    PID = $$
    Reason = $reason
    Timestamp = $timestamp
EOF

    cat << EOF

Lock acquired successfully.
You are now inside a sub-shell with the lock acquired.
To start another subshell with the same lock, run:
    BASTION_LOCK=$$ bash

Instructions to release the lock:
  type exit to release the lock (this will exit the sub-shell and you will return back to your original shell)
  alternatively, lock will be automatically released when your SSH session ends

EOF

    export BASTION_LOCK=$$
    # spawn a new bash sub-shell
    # in linux, forking a process copies parent process's file descriptors
    # so the forked shell will inherit the lock file descriptor which can lead to unexpected
    # side effects. For example,
    # > lock_bastion
    # > > sleep 60 &
    # > > exit
    # > lock_bastion <--- this will fail!
    # so we need to close the fd in the sub-shell
    bash --rcfile <(echo "exec 200>&- ; source $HOME/.bashrc")
) 200>"$HOME/nirvana_flock"

# this cleanup is good to have, but not necessary
rm "$HOME/nirvana_flock" "$HOME/nirvana_flock.info"